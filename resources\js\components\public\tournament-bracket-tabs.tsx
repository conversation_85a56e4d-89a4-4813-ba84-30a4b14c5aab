import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from '@/components/ui/tabs';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Trophy, Users, Calendar, MapPin, Target, Zap, Crown, Medal } from 'lucide-react';
import publicApi from '@/lib/public-api';

interface SportMatch {
    id: number;
    title: string;
    zone_a: string;
    zone_b: string;
    match_date: string;
    match_time: string;
    venue: string;
    status: 'scheduled' | 'ongoing' | 'completed' | 'cancelled';
    event_type: string;
    category?: string;
    court_field?: string;
    bracket_round?: string;
    is_highlighted?: boolean;
    highlight_color?: string;
    score_zone_a?: number;
    score_zone_b?: number;
    winner_zone?: string;
    sport: {
        id: number;
        name: string;
        logo_url?: string;
    };
}

interface SportsSchedule {
    id: number;
    schedule_name: string;
    description: string;
    start_date: string;
    end_date: string;
    main_venue: string;
    sport: {
        id: number;
        name: string;
        logo_url?: string;
    };
    matches: SportMatch[];
}

export default function TournamentBracketTabs() {
    const [schedules, setSchedules] = useState<SportsSchedule[]>([]);
    const [loading, setLoading] = useState(true);
    const [activeTab, setActiveTab] = useState<string>('');

    useEffect(() => {
        fetchSchedules();
    }, []);

    const fetchSchedules = async () => {
        try {
            const response = await publicApi.get('/public/jadual/sports-schedules');
            const schedulesData = response.data.data || [];
            setSchedules(schedulesData);
            
            // Set first schedule as active tab
            if (schedulesData.length > 0) {
                setActiveTab(schedulesData[0].id.toString());
            }
        } catch (error) {
            console.error('Error fetching schedules:', error);
        } finally {
            setLoading(false);
        }
    };

    const getEventTypeIcon = (eventType: string) => {
        switch (eventType) {
            case 'group_event':
                return <Users className="h-4 w-4" />;
            case 'round_robin_final':
                return <Trophy className="h-4 w-4" />;
            case 'category_based_round_robin':
                return <Target className="h-4 w-4" />;
            case 'knockout_bracket_category':
                return <Crown className="h-4 w-4" />;
            default:
                return <Medal className="h-4 w-4" />;
        }
    };

    const getEventTypeLabel = (eventType: string) => {
        switch (eventType) {
            case 'group_event':
                return 'Group Event';
            case 'round_robin_final':
                return 'Round Robin + Final';
            case 'category_based_round_robin':
                return 'Category Round Robin';
            case 'knockout_bracket_category':
                return 'Knockout + Category';
            default:
                return 'Tournament';
        }
    };

    const getStatusColor = (status: string) => {
        switch (status) {
            case 'completed':
                return 'bg-green-500/20 text-green-400 border-green-500/30';
            case 'ongoing':
                return 'bg-yellow-500/20 text-yellow-400 border-yellow-500/30';
            case 'scheduled':
                return 'bg-blue-500/20 text-blue-400 border-blue-500/30';
            case 'cancelled':
                return 'bg-red-500/20 text-red-400 border-red-500/30';
            default:
                return 'bg-gray-500/20 text-gray-400 border-gray-500/30';
        }
    };

    const getHighlightColor = (color?: string) => {
        switch (color) {
            case 'kuning':
                return 'border-yellow-400/50 bg-yellow-400/10';
            case 'merah':
                return 'border-red-400/50 bg-red-400/10';
            case 'hijau':
                return 'border-green-400/50 bg-green-400/10';
            case 'biru':
                return 'border-blue-400/50 bg-blue-400/10';
            case 'oren':
                return 'border-orange-400/50 bg-orange-400/10';
            case 'merah jambu':
                return 'border-pink-400/50 bg-pink-400/10';
            default:
                return 'border-white/10 bg-white/5';
        }
    };

    const formatDateTime = (date: string, time: string) => {
        const dateObj = new Date(`${date}T${time}`);
        return {
            date: dateObj.toLocaleDateString('ms-MY', { 
                day: 'numeric', 
                month: 'short',
                year: 'numeric'
            }),
            time: dateObj.toLocaleTimeString('ms-MY', { 
                hour: '2-digit', 
                minute: '2-digit',
                hour12: false
            })
        };
    };

    if (loading) {
        return (
            <div className="flex items-center justify-center py-12">
                <div className="text-center">
                    <Zap className="h-8 w-8 animate-spin mx-auto mb-4 text-yellow-400" />
                    <p className="text-white/70">Memuat jadual tournament...</p>
                </div>
            </div>
        );
    }

    if (schedules.length === 0) {
        return (
            <div className="text-center py-12">
                <Trophy className="h-12 w-12 mx-auto mb-4 text-white/30" />
                <p className="text-white/70">Tiada jadual tournament dijumpai</p>
            </div>
        );
    }

    return (
        <div className="w-full">
            <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
                {/* Sport Tabs */}
                <div className="mb-8">
                    <TabsList className="grid w-full grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-2 bg-black/40 backdrop-blur-sm p-2 rounded-xl border border-white/10">
                        {schedules.map((schedule) => (
                            <TabsTrigger
                                key={schedule.id}
                                value={schedule.id.toString()}
                                className="flex flex-col items-center gap-2 p-4 rounded-lg data-[state=active]:bg-yellow-400/20 data-[state=active]:text-yellow-400 data-[state=active]:border-yellow-400/30 border border-transparent transition-all duration-300 hover:bg-white/10"
                            >
                                <div className="flex items-center gap-2">
                                    {schedule.sport.logo_url && (
                                        <img 
                                            src={schedule.sport.logo_url} 
                                            alt={schedule.sport.name}
                                            className="w-6 h-6 object-contain"
                                        />
                                    )}
                                    <span className="font-medium text-sm">{schedule.sport.name}</span>
                                </div>
                                <Badge variant="outline" className="text-xs">
                                    {schedule.matches?.length || 0} perlawanan
                                </Badge>
                            </TabsTrigger>
                        ))}
                    </TabsList>
                </div>

                {/* Tournament Content */}
                {schedules.map((schedule) => (
                    <TabsContent key={schedule.id} value={schedule.id.toString()} className="space-y-6">
                        {/* Schedule Header */}
                        <Card className="bg-black/60 backdrop-blur-xl border-yellow-400/30">
                            <CardHeader>
                                <CardTitle className="flex items-center gap-3 text-white">
                                    {schedule.sport.logo_url && (
                                        <img 
                                            src={schedule.sport.logo_url} 
                                            alt={schedule.sport.name}
                                            className="w-8 h-8 object-contain"
                                        />
                                    )}
                                    <div>
                                        <h3 className="text-xl font-bold">{schedule.schedule_name}</h3>
                                        <p className="text-sm text-white/70 font-normal">{schedule.description}</p>
                                    </div>
                                </CardTitle>
                                <div className="flex flex-wrap gap-4 text-sm text-white/80">
                                    <div className="flex items-center gap-2">
                                        <Calendar className="h-4 w-4" />
                                        <span>{new Date(schedule.start_date).toLocaleDateString('ms-MY')} - {new Date(schedule.end_date).toLocaleDateString('ms-MY')}</span>
                                    </div>
                                    <div className="flex items-center gap-2">
                                        <MapPin className="h-4 w-4" />
                                        <span>{schedule.main_venue}</span>
                                    </div>
                                    <div className="flex items-center gap-2">
                                        {getEventTypeIcon(schedule.matches?.[0]?.event_type || '')}
                                        <span>{getEventTypeLabel(schedule.matches?.[0]?.event_type || '')}</span>
                                    </div>
                                </div>
                            </CardHeader>
                        </Card>

                        {/* Matches Grid */}
                        <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-4">
                            {schedule.matches?.map((match) => {
                                const dateTime = formatDateTime(match.match_date, match.match_time);
                                const hasResult = match.status === 'completed' && match.score_zone_a !== undefined && match.score_zone_b !== undefined;
                                
                                return (
                                    <Card 
                                        key={match.id} 
                                        className={`backdrop-blur-sm border transition-all duration-300 hover:scale-105 ${
                                            match.is_highlighted 
                                                ? getHighlightColor(match.highlight_color)
                                                : 'bg-white/5 border-white/10 hover:border-white/20'
                                        }`}
                                    >
                                        <CardContent className="p-4">
                                            <div className="space-y-3">
                                                {/* Match Header */}
                                                <div className="flex items-center justify-between">
                                                    <h4 className="font-medium text-white text-sm">{match.title}</h4>
                                                    <Badge className={`text-xs ${getStatusColor(match.status)}`}>
                                                        {match.status === 'scheduled' ? 'Dijadualkan' :
                                                         match.status === 'ongoing' ? 'Berlangsung' :
                                                         match.status === 'completed' ? 'Selesai' : 'Dibatalkan'}
                                                    </Badge>
                                                </div>

                                                {/* Teams */}
                                                <div className="space-y-2">
                                                    <div className="flex items-center justify-between">
                                                        <span className="text-white font-medium">Zon {match.zone_a}</span>
                                                        {hasResult && <span className="text-yellow-400 font-bold">{match.score_zone_a}</span>}
                                                    </div>
                                                    <div className="text-center text-white/50 text-xs">VS</div>
                                                    <div className="flex items-center justify-between">
                                                        <span className="text-white font-medium">Zon {match.zone_b}</span>
                                                        {hasResult && <span className="text-yellow-400 font-bold">{match.score_zone_b}</span>}
                                                    </div>
                                                </div>

                                                {/* Match Details */}
                                                <div className="space-y-1 text-xs text-white/70">
                                                    <div className="flex items-center gap-2">
                                                        <Calendar className="h-3 w-3" />
                                                        <span>{dateTime.date} • {dateTime.time}</span>
                                                    </div>
                                                    <div className="flex items-center gap-2">
                                                        <MapPin className="h-3 w-3" />
                                                        <span>{match.venue}</span>
                                                    </div>
                                                    {match.court_field && (
                                                        <div className="flex items-center gap-2">
                                                            <Target className="h-3 w-3" />
                                                            <span>Court {match.court_field}</span>
                                                        </div>
                                                    )}
                                                    {match.category && (
                                                        <div className="flex items-center gap-2">
                                                            <Medal className="h-3 w-3" />
                                                            <span>{match.category}</span>
                                                        </div>
                                                    )}
                                                </div>

                                                {/* Winner */}
                                                {match.winner_zone && (
                                                    <div className="pt-2 border-t border-white/10">
                                                        <div className="flex items-center gap-2 text-green-400">
                                                            <Crown className="h-3 w-3" />
                                                            <span className="text-xs font-medium">Pemenang: Zon {match.winner_zone}</span>
                                                        </div>
                                                    </div>
                                                )}
                                            </div>
                                        </CardContent>
                                    </Card>
                                );
                            })}
                        </div>

                        {/* No matches message */}
                        {(!schedule.matches || schedule.matches.length === 0) && (
                            <div className="text-center py-8">
                                <Trophy className="h-12 w-12 mx-auto mb-4 text-white/30" />
                                <p className="text-white/70">Tiada perlawanan dijadualkan untuk sukan ini</p>
                            </div>
                        )}
                    </TabsContent>
                ))}
            </Tabs>
        </div>
    );
}
