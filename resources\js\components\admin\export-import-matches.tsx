import { useState } from 'react';
import api from '@/lib/axios';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import { Download, Upload, FileSpreadsheet, FileText, Calendar } from 'lucide-react';
import { toast } from 'react-hot-toast';

interface ExportImportMatchesProps {
    sports: Array<{ id: number; name: string }>;
    sportsSchedules: Array<{ id: number; schedule_name: string; sport_id: number }>;
}

export default function ExportImportMatches({ sports, sportsSchedules }: ExportImportMatchesProps) {
    const [isExporting, setIsExporting] = useState(false);
    const [isImporting, setIsImporting] = useState(false);
    const [exportFormat, setExportFormat] = useState('excel');
    const [exportSport, setExportSport] = useState('');
    const [exportSchedule, setExportSchedule] = useState('');
    const [importFile, setImportFile] = useState<File | null>(null);

    const handleExport = async () => {
        try {
            setIsExporting(true);
            
            const params = new URLSearchParams();
            if (exportSport) params.append('sport_id', exportSport);
            if (exportSchedule) params.append('sports_schedule_id', exportSchedule);
            params.append('format', exportFormat);

            const response = await api.get(`/admin/sport-matches/export?${params.toString()}`, {
                responseType: 'blob'
            });

            // Create download link
            const url = window.URL.createObjectURL(new Blob([response.data]));
            const link = document.createElement('a');
            link.href = url;
            
            const extension = exportFormat === 'excel' ? 'xlsx' : 'csv';
            const filename = `jadual-pertandingan-${new Date().toISOString().split('T')[0]}.${extension}`;
            link.setAttribute('download', filename);
            
            document.body.appendChild(link);
            link.click();
            link.remove();
            window.URL.revokeObjectURL(url);

            toast.success('Export berjaya dimuat turun');
        } catch (error) {
            console.error('Export error:', error);
            toast.error('Gagal export data');
        } finally {
            setIsExporting(false);
        }
    };

    const handleImport = async () => {
        if (!importFile) {
            toast.error('Sila pilih fail untuk import');
            return;
        }

        try {
            setIsImporting(true);
            
            const formData = new FormData();
            formData.append('file', importFile);

            const response = await api.post('/admin/sport-matches/import', formData, {
                headers: {
                    'Content-Type': 'multipart/form-data'
                }
            });

            toast.success(`Import berjaya: ${response.data.imported} perlawanan ditambah`);
            setImportFile(null);
            
            // Reset file input
            const fileInput = document.getElementById('import-file') as HTMLInputElement;
            if (fileInput) fileInput.value = '';

        } catch (error: any) {
            console.error('Import error:', error);
            const message = error.response?.data?.message || 'Gagal import data';
            toast.error(message);
        } finally {
            setIsImporting(false);
        }
    };

    const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
        const file = event.target.files?.[0];
        if (file) {
            // Validate file type
            const allowedTypes = [
                'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // .xlsx
                'application/vnd.ms-excel', // .xls
                'text/csv' // .csv
            ];
            
            if (!allowedTypes.includes(file.type)) {
                toast.error('Format fail tidak disokong. Sila gunakan Excel (.xlsx, .xls) atau CSV');
                return;
            }

            setImportFile(file);
        }
    };

    const downloadTemplate = async () => {
        try {
            const response = await api.get('/admin/sport-matches/template', {
                responseType: 'blob'
            });

            const url = window.URL.createObjectURL(new Blob([response.data]));
            const link = document.createElement('a');
            link.href = url;
            link.setAttribute('download', 'template-jadual-pertandingan.xlsx');
            
            document.body.appendChild(link);
            link.click();
            link.remove();
            window.URL.revokeObjectURL(url);

            toast.success('Template berjaya dimuat turun');
        } catch (error) {
            console.error('Template download error:', error);
            toast.error('Gagal muat turun template');
        }
    };

    const filteredSchedules = sportsSchedules.filter(
        schedule => !exportSport || schedule.sport_id.toString() === exportSport
    );

    return (
        <div className="grid gap-6 md:grid-cols-2">
            {/* Export Section */}
            <Card>
                <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                        <Download className="h-5 w-5" />
                        Export Data
                    </CardTitle>
                    <CardDescription>
                        Muat turun data jadual pertandingan dalam format Excel atau CSV
                    </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                    <div>
                        <Label htmlFor="export-format">Format Export</Label>
                        <Select value={exportFormat} onValueChange={setExportFormat}>
                            <SelectTrigger>
                                <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                                <SelectItem value="excel">
                                    <div className="flex items-center gap-2">
                                        <FileSpreadsheet className="h-4 w-4" />
                                        Excel (.xlsx)
                                    </div>
                                </SelectItem>
                                <SelectItem value="csv">
                                    <div className="flex items-center gap-2">
                                        <FileText className="h-4 w-4" />
                                        CSV (.csv)
                                    </div>
                                </SelectItem>
                            </SelectContent>
                        </Select>
                    </div>

                    <div>
                        <Label htmlFor="export-sport">Filter Sukan (Opsional)</Label>
                        <Select value={exportSport} onValueChange={setExportSport}>
                            <SelectTrigger>
                                <SelectValue placeholder="Semua sukan" />
                            </SelectTrigger>
                            <SelectContent>
                                <SelectItem value="">Semua sukan</SelectItem>
                                {sports.map((sport) => (
                                    <SelectItem key={sport.id} value={sport.id.toString()}>
                                        {sport.name}
                                    </SelectItem>
                                ))}
                            </SelectContent>
                        </Select>
                    </div>

                    <div>
                        <Label htmlFor="export-schedule">Filter Jadual (Opsional)</Label>
                        <Select value={exportSchedule} onValueChange={setExportSchedule}>
                            <SelectTrigger>
                                <SelectValue placeholder="Semua jadual" />
                            </SelectTrigger>
                            <SelectContent>
                                <SelectItem value="">Semua jadual</SelectItem>
                                {filteredSchedules.map((schedule) => (
                                    <SelectItem key={schedule.id} value={schedule.id.toString()}>
                                        {schedule.schedule_name}
                                    </SelectItem>
                                ))}
                            </SelectContent>
                        </Select>
                    </div>

                    <Button 
                        onClick={handleExport} 
                        disabled={isExporting}
                        className="w-full"
                    >
                        {isExporting ? 'Mengexport...' : 'Export Data'}
                    </Button>
                </CardContent>
            </Card>

            {/* Import Section */}
            <Card>
                <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                        <Upload className="h-5 w-5" />
                        Import Data
                    </CardTitle>
                    <CardDescription>
                        Muat naik data jadual pertandingan dari fail Excel atau CSV
                    </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                    <div>
                        <Label htmlFor="import-file">Pilih Fail</Label>
                        <Input
                            id="import-file"
                            type="file"
                            accept=".xlsx,.xls,.csv"
                            onChange={handleFileChange}
                        />
                        <p className="text-xs text-gray-600 mt-1">
                            Format yang disokong: Excel (.xlsx, .xls), CSV (.csv)
                        </p>
                    </div>

                    {importFile && (
                        <div className="p-3 bg-blue-50 rounded-lg">
                            <div className="flex items-center gap-2">
                                <FileSpreadsheet className="h-4 w-4 text-blue-600" />
                                <span className="text-sm font-medium">{importFile.name}</span>
                            </div>
                            <p className="text-xs text-gray-600 mt-1">
                                Saiz: {(importFile.size / 1024).toFixed(1)} KB
                            </p>
                        </div>
                    )}

                    <Button 
                        onClick={handleImport} 
                        disabled={isImporting || !importFile}
                        className="w-full"
                    >
                        {isImporting ? 'Mengimport...' : 'Import Data'}
                    </Button>

                    <Separator />

                    <div className="text-center">
                        <p className="text-sm text-gray-600 mb-2">
                            Perlukan template untuk import?
                        </p>
                        <Button 
                            variant="outline" 
                            size="sm"
                            onClick={downloadTemplate}
                            className="flex items-center gap-2"
                        >
                            <Download className="h-4 w-4" />
                            Muat Turun Template
                        </Button>
                    </div>
                </CardContent>
            </Card>
        </div>
    );
}
