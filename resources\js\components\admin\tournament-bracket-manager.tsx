
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Trophy } from 'lucide-react';
import ModernTournamentBracket from './modern-tournament-bracket';

interface TournamentBracketManagerProps {
    onMatchSelect?: (match: any) => void;
}

export default function TournamentBracketManager({ onMatchSelect }: TournamentBracketManagerProps) {
    return (
        <div className="space-y-6">
            {/* Modern Tournament Bracket with integrated controls */}
            <div className="space-y-6">
                <ModernTournamentBracket onMatchSelect={onMatchSelect} />
            </div>
        </div>
    );
};
