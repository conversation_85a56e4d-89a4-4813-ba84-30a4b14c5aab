import { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import api from '@/lib/axios';

interface User {
  id: number;
  username: string;
  name: string;
  email: string;
  role: string;
  zone?: string;
  must_change_password: boolean;
}

interface AuthContextType {
  user: User | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  login: (username: string, password: string, remember?: boolean) => Promise<any>;
  logout: () => Promise<void>;
  updatePassword: (currentPassword: string, newPassword: string) => Promise<void>;
  checkAuth: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  const isAuthenticated = !!user && (typeof window !== 'undefined' ? !!localStorage.getItem('auth_token') : false);

  const checkAuth = async () => {
    try {
      // Check if we're in browser environment
      if (typeof window === 'undefined') {
        setIsLoading(false);
        return;
      }

      const token = localStorage.getItem('auth_token');

      if (!token) {
        setUser(null);
        setIsLoading(false);
        return;
      }

      const response = await api.get('/user');
      setUser(response.data);

      // Save user data to localStorage for persistence
      localStorage.setItem('user_data', JSON.stringify(response.data));
    } catch (error) {
      setUser(null);
      if (typeof window !== 'undefined') {
        localStorage.removeItem('auth_token');
      }
    } finally {
      setIsLoading(false);
    }
  };

  const login = async (username: string, password: string, remember = false) => {
    try {
      const response = await api.post('/login', {
        username,
        password,
        remember,
      });

      if (response.data.token) {
        localStorage.setItem('auth_token', response.data.token);
      }

      setUser(response.data.user);

      // Save user data to localStorage for persistence
      localStorage.setItem('user_data', JSON.stringify(response.data.user));

      return response.data;
    } catch (error) {
      throw error;
    }
  };

  const logout = async () => {
    try {
      await api.post('/logout');
    } catch (error) {
      // Continue with logout even if API call fails
    } finally {
      setUser(null);
      localStorage.removeItem('auth_token');
      localStorage.removeItem('user_data');
    }
  };

  const updatePassword = async (currentPassword: string, newPassword: string) => {
    try {
      const response = await api.put('/password/change', {
        current_password: currentPassword,
        password: newPassword,
        password_confirmation: newPassword,
      });

      // Update user state to reflect password change
      if (user) {
        setUser({
          ...user,
          must_change_password: false,
        });
      }

      return response.data;
    } catch (error) {
      throw error;
    }
  };

  useEffect(() => {
    // Only run in browser environment
    if (typeof window === 'undefined') {
      setIsLoading(false);
      return;
    }

    // Check if we have a token in localStorage
    const token = localStorage.getItem('auth_token');
    const userData = localStorage.getItem('user_data');

    if (token) {
      // If we have a token, check auth status
      checkAuth();
    } else {
      // No token, but check if we have user data (shouldn't happen)
      if (userData) {
        localStorage.removeItem('user_data');
      }
      setIsLoading(false);
    }
  }, []);

  const value = {
    user,
    isLoading,
    isAuthenticated,
    login,
    logout,
    updatePassword,
    checkAuth,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}
