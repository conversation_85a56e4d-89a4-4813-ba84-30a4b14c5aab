import React, { useState, useEffect } from 'react';
import { Newspaper, Calendar, Eye, Tag, AlertCircle, Loader2 } from 'lucide-react';
import { usePageContent } from '@/hooks/usePageContent';
import { useAuth } from '@/contexts/auth-context';
import api from '@/lib/axios';
import toast from 'react-hot-toast';

// Components
import NewsCard from '@/components/news/NewsCard';
import ViewNewsModal from '@/components/news/ViewNewsModal';
import CreateNewsModal from '@/components/news/CreateNewsModal';
import EditNewsModal from '@/components/news/EditNewsModal';
import DeleteConfirmModal from '@/components/news/DeleteConfirmModal';

interface NewsItem {
    id: number;
    title: string;
    excerpt: string;
    content?: string;
    featured_image?: string;
    author: string;
    published_at: string;
    category: string;
    tags: string[];
    views: number;
    is_important: boolean;
    zone: string;
}

export default function News() {
    const [selectedCategory, setSelectedCategory] = useState<string>('all');
    const { data: content = {}, isLoading: loading } = usePageContent('news');
    const { isAuthenticated, user, isLoading: authLoading } = useAuth();

    // News data states
    const [newsItems, setNewsItems] = useState<NewsItem[]>([]);
    const [isLoadingNews, setIsLoadingNews] = useState(true);

    // Modal states
    const [viewModalOpen, setViewModalOpen] = useState(false);
    const [editModalOpen, setEditModalOpen] = useState(false);
    const [deleteModalOpen, setDeleteModalOpen] = useState(false);
    const [uploadModalOpen, setUploadModalOpen] = useState(false);
    const [selectedNews, setSelectedNews] = useState<NewsItem | null>(null);

    // Load news data from API
    const loadNewsData = async () => {
        try {
            setIsLoadingNews(true);
            const response = await api.get('/public/news');

            if (response.data && response.data.data) {
                setNewsItems(response.data.data);
            }
        } catch (error) {
            console.error('Failed to load news data:', error);
            toast.error('Failed to load news data');
        } finally {
            setIsLoadingNews(false);
        }
    };

    // Load news data on component mount
    useEffect(() => {
        loadNewsData();
    }, []);

    // Filter news items by category
    const filteredItems = selectedCategory === 'all'
        ? newsItems
        : newsItems.filter(item => item.category === selectedCategory);

    // Get unique categories for filter
    const categories = ['all', ...Array.from(new Set(newsItems.map(item => item.category)))];

    // Get CMS content with fallbacks
    const heroTitle = content?.hero?.title || 'BERITA';
    const heroSubtitle = content?.hero?.subtitle || 'Berita terkini mengenai Sukan Intra Kor Kesihatan DiRaja 2025';
    const heroDescription = content?.hero?.description || 'Dapatkan maklumat terkini mengenai acara, jadual, dan keputusan Sukan Intra Kor Kesihatan DiRaja 2025.';
    const heroBackgroundImage = content?.hero?.background_image || '/images/default.jpg';

    // Modal handlers
    const handleView = async (item: NewsItem) => {
        try {
            // Get full article content
            const response = await api.get(`/public/news/${item.id}`);
            if (response.data) {
                setSelectedNews(response.data);
                setViewModalOpen(true);
            }
        } catch (error) {
            console.error('Failed to load news article:', error);
            toast.error('Failed to load news article');
        }
    };

    const handleEdit = async (item: NewsItem) => {
        try {
            // Get full article content for editing
            const response = await api.get(`/public/news/${item.id}`);
            if (response.data) {
                setSelectedNews(response.data);
                setEditModalOpen(true);
            }
        } catch (error) {
            console.error('Failed to load news article for editing:', error);
            toast.error('Failed to load news article for editing');
        }
    };

    const handleDelete = (item: NewsItem) => {
        setSelectedNews(item);
        setDeleteModalOpen(true);
    };

    const handleShare = (item: NewsItem) => {
        if (navigator.share) {
            navigator.share({
                title: item.title,
                text: item.excerpt,
                url: window.location.href
            });
        } else {
            // Fallback to clipboard
            navigator.clipboard.writeText(window.location.href);
            toast.success('Link copied to clipboard!');
        }
    };

    const handleUploadSuccess = () => {
        loadNewsData();
        setUploadModalOpen(false);
        toast.success('Berita berjaya dimuat naik!');
    };

    const handleEditSuccess = () => {
        loadNewsData();
        setEditModalOpen(false);
        toast.success('Berita berjaya dikemas kini!');
    };

    const handleDeleteSuccess = () => {
        loadNewsData();
        setDeleteModalOpen(false);
        toast.success('Berita berjaya dipadam!');
    };

    const importantNews = newsItems.filter(article => article.is_important);
    const totalViews = newsItems.reduce((sum, article) => sum + article.views, 0);

    if (loading || authLoading || isLoadingNews) {
        return (
            <div className="min-h-screen bg-gradient-to-br from-gray-900 to-black flex items-center justify-center">
                <div className="text-center">
                    <Loader2 className="w-12 h-12 text-yellow-400 animate-spin mx-auto mb-4" />
                    <p className="text-white text-lg">Memuat berita...</p>
                </div>
            </div>
        );
    }

    return (

        <div className="min-h-screen bg-gray-50">
            {/* Hero Section */}
            <section
                className="relative min-h-screen bg-cover bg-center bg-no-repeat pt-32 2xl:pt-40 xl:pt-36 lg:pt-28 md:pt-28 sm:pt-24"
                style={{
                    backgroundImage: `linear-gradient(rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0.5)), url('${heroBackgroundImage}')`
                }}
            >
                <div className="container mx-auto px-6 2xl:px-8 xl:px-6 lg:px-4 md:px-4 sm:px-4 py-16 2xl:py-20 xl:py-18 lg:py-12 md:py-12 sm:py-8">
                    <div className="2xl:max-w-7xl xl:max-w-7xl lg:max-w-5xl md:max-w-5xl sm:max-w-4xl max-w-full mx-auto text-center">
                        <div className="inline-flex items-center justify-center w-20 h-20 2xl:w-24 2xl:h-24 xl:w-22 xl:h-22 lg:w-16 lg:h-16 md:w-18 md:h-18 sm:w-16 sm:h-16 bg-yellow-500/20 rounded-full mb-6 2xl:mb-8 xl:mb-7 lg:mb-4 md:mb-5 sm:mb-4">
                            <Newspaper className="w-10 h-10 2xl:w-12 2xl:h-12 xl:w-11 xl:h-11 lg:w-8 lg:h-8 md:w-9 md:h-9 sm:w-8 sm:h-8 text-yellow-400" />
                        </div>
                        <h1 className="text-5xl md:text-6xl 2xl:text-8xl xl:text-7xl lg:text-4xl sm:text-4xl font-black text-white mb-6 2xl:mb-8 xl:mb-7 lg:mb-4 md:mb-5 sm:mb-4 drop-shadow-lg">
                            <span className="text-yellow-400">{heroTitle}</span>
                        </h1>
                        <p className="text-xl 2xl:text-2xl xl:text-xl lg:text-base md:text-base sm:text-sm text-white/90 mb-6 2xl:mb-8 xl:mb-7 lg:mb-4 md:mb-5 sm:mb-4 leading-relaxed max-w-3xl mx-auto">
                            {heroSubtitle}
                        </p>
                        <p className="text-lg 2xl:text-xl xl:text-lg lg:text-sm md:text-base sm:text-sm text-white/80 mb-12 2xl:mb-16 xl:mb-14 lg:mb-8 md:mb-10 sm:mb-8 leading-relaxed max-w-4xl mx-auto">
                            {heroDescription}
                        </p>

                        {/* Stats Cards */}
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 2xl:gap-8 xl:gap-6 lg:gap-4 md:gap-4 sm:gap-4 max-w-4xl mx-auto mb-8">
                            <div className="bg-black/40 backdrop-blur-xl rounded-2xl 2xl:rounded-3xl xl:rounded-2xl lg:rounded-xl md:rounded-xl sm:rounded-lg p-6 2xl:p-8 xl:p-7 lg:p-4 md:p-5 sm:p-4 border border-yellow-400/30">
                                <div className="text-3xl 2xl:text-4xl xl:text-3xl lg:text-xl md:text-2xl sm:text-xl font-bold text-yellow-400 mb-2">
                                    {newsItems.length}
                                </div>
                                <div className="text-white/80 text-sm 2xl:text-base xl:text-sm lg:text-xs md:text-sm sm:text-xs">
                                    Artikel Berita
                                </div>
                            </div>
                            <div className="bg-black/40 backdrop-blur-xl rounded-2xl 2xl:rounded-3xl xl:rounded-2xl lg:rounded-xl md:rounded-xl sm:rounded-lg p-6 2xl:p-8 xl:p-7 lg:p-4 md:p-5 sm:p-4 border border-yellow-400/30">
                                <div className="text-3xl 2xl:text-4xl xl:text-3xl lg:text-xl md:text-2xl sm:text-xl font-bold text-yellow-400 mb-2">
                                    {importantNews.length}
                                </div>
                                <div className="text-white/80 text-sm 2xl:text-base xl:text-sm lg:text-xs md:text-sm sm:text-xs">
                                    Berita Penting
                                </div>
                            </div>
                            <div className="bg-black/40 backdrop-blur-xl rounded-2xl 2xl:rounded-3xl xl:rounded-2xl lg:rounded-xl md:rounded-xl sm:rounded-lg p-6 2xl:p-8 xl:p-7 lg:p-4 md:p-5 sm:p-4 border border-yellow-400/30">
                                <div className="text-3xl 2xl:text-4xl xl:text-3xl lg:text-xl md:text-2xl sm:text-xl font-bold text-yellow-400 mb-2">
                                    {totalViews.toLocaleString()}
                                </div>
                                <div className="text-white/80 text-sm 2xl:text-base xl:text-sm lg:text-xs md:text-sm sm:text-xs">
                                    Total Tontonan
                                </div>
                            </div>
                        </div>

                        {/* Action Button for Authenticated Users */}
                        {isAuthenticated && (user?.role === 'superadmin' || user?.role === 'admin' || user?.role === 'media') && (
                            <div className="flex justify-center">
                                <button
                                    onClick={() => setUploadModalOpen(true)}
                                    className="inline-flex items-center gap-3 bg-yellow-500 hover:bg-yellow-600 text-black px-8 py-4 2xl:px-10 2xl:py-5 xl:px-8 xl:py-4 lg:px-6 lg:py-3 md:px-6 md:py-3 sm:px-4 sm:py-2 rounded-xl 2xl:rounded-2xl xl:rounded-xl lg:rounded-lg md:rounded-lg sm:rounded-md font-bold text-lg 2xl:text-xl xl:text-lg lg:text-base md:text-base sm:text-sm transition-all duration-300 hover:scale-105 shadow-2xl"
                                >
                                    <Newspaper className="w-6 h-6 2xl:w-7 2xl:h-7 xl:w-6 xl:h-6 lg:w-5 lg:h-5 md:w-5 md:h-5 sm:w-4 sm:h-4" />
                                    <span className="hidden sm:inline">Cipta Berita Baru</span>
                                    <span className="sm:hidden">Cipta</span>
                                </button>
                            </div>
                        )}
                    </div>
                </div>
            </section>



            {/* News Section */}
            <section className="py-20 2xl:py-28 xl:py-24 lg:py-12 md:py-16 sm:py-12 bg-gradient-to-br from-gray-900 to-black">
                <div className="container mx-auto px-6 2xl:px-8 xl:px-6 lg:px-4 md:px-4 sm:px-4 2xl:max-w-7xl xl:max-w-7xl lg:max-w-5xl md:max-w-5xl sm:max-w-4xl max-w-full">
                    <div className="text-center mb-16 2xl:mb-20 xl:mb-18 lg:mb-8 md:mb-12 sm:mb-8">
                        <h2 className="text-5xl 2xl:text-7xl xl:text-6xl lg:text-3xl md:text-4xl sm:text-3xl font-black text-white mb-6 2xl:mb-8 xl:mb-7 lg:mb-4 md:mb-5 sm:mb-4">
                            <span className="text-yellow-400">BERITA</span> TERKINI
                        </h2>
                        <p className="text-xl 2xl:text-2xl xl:text-xl lg:text-sm md:text-base sm:text-sm text-gray-300 max-w-3xl mx-auto">
                            {newsItems.length} artikel berita tersedia
                        </p>
                    </div>

                    {/* Category Filter */}
                    <div className="flex flex-wrap justify-center gap-3 mb-12">
                        {categories.map(category => (
                            <button
                                key={category}
                                onClick={() => setSelectedCategory(category)}
                                className={`px-4 py-2 rounded-full text-sm font-semibold transition-all duration-300 ${
                                    selectedCategory === category
                                        ? 'bg-yellow-500 text-black'
                                        : 'bg-black/40 text-white border border-yellow-400/30 hover:border-yellow-400/60'
                                }`}
                            >
                                {category === 'all' ? 'Semua' : category.charAt(0).toUpperCase() + category.slice(1)}
                            </button>
                        ))}
                    </div>

                    {/* News Grid */}
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-2 xl:grid-cols-3 2xl:grid-cols-4 gap-8 2xl:gap-10 xl:gap-8 lg:gap-6 md:gap-6 sm:gap-4">
                        {filteredItems.map(item => (
                            <NewsCard
                                key={item.id}
                                item={item}
                                onView={handleView}
                                onEdit={handleEdit}
                                onDelete={handleDelete}
                            />
                        ))}
                    </div>

                    {/* Empty State */}
                    {filteredItems.length === 0 && (
                        <div className="text-center py-16">
                            <Newspaper className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                            <h3 className="text-xl font-semibold text-white mb-2">Tiada Berita Ditemui</h3>
                            <p className="text-gray-400">
                                {selectedCategory === 'all'
                                    ? 'Belum ada berita yang diterbitkan.'
                                    : `Tiada berita dalam kategori "${selectedCategory}".`
                                }
                            </p>
                        </div>
                    )}
                </div>
            </section>

            {/* Modals */}
            <ViewNewsModal
                isOpen={viewModalOpen}
                onClose={() => setViewModalOpen(false)}
                news={selectedNews}
                onShare={handleShare}
            />

            <CreateNewsModal
                isOpen={uploadModalOpen}
                onClose={() => setUploadModalOpen(false)}
                onSuccess={handleUploadSuccess}
            />

            <EditNewsModal
                isOpen={editModalOpen}
                onClose={() => setEditModalOpen(false)}
                onSuccess={handleEditSuccess}
                news={selectedNews}
            />

            <DeleteConfirmModal
                isOpen={deleteModalOpen}
                onClose={() => setDeleteModalOpen(false)}
                onSuccess={handleDeleteSuccess}
                news={selectedNews}
            />
        </div>
    );
}

