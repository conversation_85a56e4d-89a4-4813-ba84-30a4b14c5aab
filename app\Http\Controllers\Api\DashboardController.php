<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\Player;
use App\Models\GameMatch;
use App\Models\Sport;
use App\Models\Zone;
use App\Models\Gallery;
use App\Models\News;
use Illuminate\Http\Request;

class DashboardController extends Controller
{
    /**
     * Get dashboard statistics
     */
    public function stats(Request $request)
    {
        try {
            $user = $request->user();

            // Initialize stats with default values
            $stats = [
                'totalPlayers' => 0,
                'totalMatches' => 0,
                'totalSports' => 0,
                'activeZones' => 0,
                'upcomingMatches' => 0,
                'completedMatches' => 0,
            ];

            // Get base statistics
            $stats['totalSports'] = Sport::where('is_active', true)->count();
            $stats['activeZones'] = Zone::where('is_active', true)->count();

            // Role-based stats filtering
            if ($user) {
                switch ($user->role) {
                    case 'zone':
                        // Filter stats for specific zone
                        $stats['totalPlayers'] = Player::where('zone', $user->zone)
                                                  ->where('is_active', true)
                                                  ->count();

                        // For matches, filter by zone code
                        $stats['upcomingMatches'] = \DB::table('matches')
                                                        ->where('status', 'scheduled')
                                                        ->where(function ($q) use ($user) {
                                                            $q->where('zone_a', $user->zone)
                                                              ->orWhere('zone_b', $user->zone);
                                                        })
                                                        ->count();
                        $stats['completedMatches'] = \DB::table('matches')
                                                         ->where('status', 'completed')
                                                         ->where(function ($q) use ($user) {
                                                             $q->where('zone_a', $user->zone)
                                                               ->orWhere('zone_b', $user->zone);
                                                         })
                                                         ->count();

                        // Total matches for this zone
                        $stats['totalMatches'] = $stats['upcomingMatches'] + $stats['completedMatches'];
                        break;

                    case 'media':
                        // Media-specific stats
                        $stats['totalContent'] = News::where('status', 'published')->count();
                        $stats['totalPhotos'] = Gallery::where('file_type', 'image')->count();
                        $stats['totalVideos'] = Gallery::where('file_type', 'video')->count();
                        $stats['totalGalleryItems'] = Gallery::count();

                        // Get general stats for media role
                        $stats['totalPlayers'] = Player::where('is_active', true)->count();
                        $stats['totalMatches'] = \DB::table('matches')->count();
                        $stats['upcomingMatches'] = \DB::table('matches')->where('status', 'scheduled')->count();
                        $stats['completedMatches'] = \DB::table('matches')->where('status', 'completed')->count();
                        break;

                    default:
                        // Admin and other roles get all stats
                        $stats['totalPlayers'] = Player::where('is_active', true)->count();
                        $stats['totalMatches'] = \DB::table('matches')->count();
                        $stats['upcomingMatches'] = \DB::table('matches')->where('status', 'scheduled')->count();
                        $stats['completedMatches'] = \DB::table('matches')->where('status', 'completed')->count();
                        break;
                }
            }

            return response()->json($stats);
        } catch (\Exception $e) {
            \Log::error('Dashboard stats error: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to fetch stats: ' . $e->getMessage()], 500);
        }
    }
}
