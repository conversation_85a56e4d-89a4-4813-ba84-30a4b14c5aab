import { useState, useEffect } from 'react';
import { Camera, Video, Image, Calendar, Eye, Play, Download, Share2, Upload, Plus, X, Loader2 } from 'lucide-react';
import { usePageContent } from '@/hooks/usePageContent';
import OptimizedImage from '@/components/OptimizedImage';
import { useAuth } from '@/contexts/auth-context';
import api from '@/lib/axios';
import toast from 'react-hot-toast';
import { z } from 'zod';

// Zod validation schema for upload
const uploadFormSchema = z.object({
    title: z.string()
        .min(1, 'Title diperlukan')
        .max(100, 'Title tidak boleh melebihi 100 aksara'),
    description: z.string()
        .min(1, 'Description diperlukan')
        .max(500, 'Description tidak boleh melebihi 500 aksara'),
    category: z.string()
        .min(1, 'Category diperlukan'),
    zone: z.string()
        .optional() // Zone is optional, will be handled by backend
});

type UploadForm = z.infer<typeof uploadFormSchema>;

interface GalleryItem {
    id: number;
    title: string;
    description: string;
    file_url: string;
    file_type: 'image' | 'video';
    category: string;
    zone: string;
    views: number;
    is_featured: boolean;
    uploaded_at: string;
    uploader?: string;
}

// Sample gallery data for Portal Sukan Intra KKD (fallback)
const sampleGalleryItems = [
    {
        id: 1,
        title: 'Majlis Perasmian Sukan Intra KKD 2025',
        description: 'Majlis perasmian rasmi Sukan Intra Kor Kesihatan DiRaja 2025 di PU Sendayan',
        file_url: '/images/default.jpg',
        file_type: 'image',
        category: 'ceremony',
        uploaded_at: '2025-07-20',
        views: 1245,
        zone: 'Semua Zone',
        is_featured: true
    },
    {
        id: 2,
        title: 'Pertandingan Bola Sepak Zone A vs Zone B',
        description: 'Aksi-aksi menarik dari perlawanan bola sepak antara Zone A dan Zone B',
        file_url: '/images/default.jpg',
        file_type: 'video',
        category: 'football',
        uploaded_at: '2025-07-21',
        views: 892,
        zone: 'Zone A',
        is_featured: false
    },
    {
        id: 3,
        title: 'Kejohanan Bola Jaring Wanita',
        description: 'Koleksi foto pertandingan bola jaring kategori wanita yang sengit',
        imageUrl: '/images/default.jpg',
        type: 'image',
        category: 'netball',
        uploadedAt: '2025-07-22',
        views: 567,
        zone: 'Zone B'
    },
    {
        id: 4,
        title: 'Larian 10x400m - Highlight',
        description: 'Video highlight larian 10x400m yang mencabar dan penuh aksi',
        imageUrl: '/images/default.jpg',
        type: 'video',
        category: 'relay',
        uploadedAt: '2025-07-23',
        views: 734,
        zone: 'Zone C'
    },
    {
        id: 5,
        title: 'Pertandingan Badminton Lelaki',
        description: 'Foto-foto aksi pertandingan badminton kategori lelaki yang memukau',
        imageUrl: '/images/default.jpg',
        type: 'image',
        category: 'badminton',
        uploadedAt: '2025-07-24',
        views: 445,
        zone: 'Zone A'
    },
    {
        id: 6,
        title: 'Sukan Rakyat - Tarik Tali',
        description: 'Pertandingan sukan rakyat tarik tali yang meriah dan menghiburkan',
        imageUrl: '/images/default.jpg',
        type: 'image',
        category: 'traditional',
        uploadedAt: '2025-07-25',
        views: 623,
        zone: 'Zone B'
    },
    {
        id: 7,
        title: 'Ping Pong Championship',
        description: 'Video kompilasi aksi terbaik dari kejohanan ping pong',
        imageUrl: '/images/default.jpg',
        type: 'video',
        category: 'table-tennis',
        uploadedAt: '2025-07-25',
        views: 389,
        zone: 'Zone C'
    },
    {
        id: 8,
        title: 'Bola Tampar Campuran',
        description: 'Foto-foto pertandingan bola tampar kategori campuran yang sengit',
        imageUrl: '/images/default.jpg',
        type: 'image',
        category: 'volleyball',
        uploadedAt: '2025-07-26',
        views: 512,
        zone: 'Zone A'
    }
];

const getCategoryColor = (categoryId: string) => {
    const categories = [
        { id: 'ceremony', color: 'bg-purple-500/20 text-purple-300 border-purple-500/30' },
        { id: 'football', color: 'bg-red-500/20 text-red-300 border-red-500/30' },
        { id: 'netball', color: 'bg-pink-500/20 text-pink-300 border-pink-500/30' },
        { id: 'relay', color: 'bg-teal-500/20 text-teal-300 border-teal-500/30' },
        { id: 'badminton', color: 'bg-blue-500/20 text-blue-300 border-blue-500/30' },
        { id: 'volleyball', color: 'bg-orange-500/20 text-orange-300 border-orange-500/30' },
        { id: 'table-tennis', color: 'bg-yellow-500/20 text-yellow-300 border-yellow-500/30' },
        { id: 'traditional', color: 'bg-green-500/20 text-green-300 border-green-500/30' }
    ];
    const category = categories.find(c => c.id === categoryId);
    return category?.color || 'bg-gray-500/20 text-gray-300 border-gray-500/30';
};

const getCategoryName = (categoryId: string) => {
    const categories = [
        { id: 'ceremony', name: 'Majlis Rasmi' },
        { id: 'football', name: 'Bola Sepak' },
        { id: 'netball', name: 'Bola Jaring' },
        { id: 'relay', name: 'Larian 10x400m' },
        { id: 'badminton', name: 'Badminton' },
        { id: 'volleyball', name: 'Bola Tampar' },
        { id: 'table-tennis', name: 'Ping Pong' },
        { id: 'traditional', name: 'Sukan Rakyat' }
    ];
    const category = categories.find(c => c.id === categoryId);
    return category?.name || categoryId;
};

const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('ms-MY', {
        day: 'numeric',
        month: 'long',
        year: 'numeric'
    });
};

export default function Gallery() {
    const [selectedCategory, setSelectedCategory] = useState<string>('all');
    const { data: content = {}, isLoading: loading } = usePageContent('gallery');
    const { isAuthenticated, user, isLoading: authLoading } = useAuth();



    // Gallery data states
    const [galleryItems, setGalleryItems] = useState<GalleryItem[]>([]);
    const [isLoadingGallery, setIsLoadingGallery] = useState(true);

    // Upload states
    const [isUploadModalOpen, setIsUploadModalOpen] = useState(false);
    const [isUploading, setIsUploading] = useState(false);
    const [imageFile, setImageFile] = useState<File | null>(null);
    const [imagePreview, setImagePreview] = useState<string>('');
    const [uploadForm, setUploadForm] = useState<UploadForm>({
        title: '',
        description: '',
        category: '',
        zone: ''
    });
    const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});

    // Load gallery data from API
    const loadGalleryData = async () => {
        try {
            setIsLoadingGallery(true);
            const response = await api.get('/public/gallery');

            if (response.data && response.data.data) {
                setGalleryItems(response.data.data);
            } else {
                // Fallback to sample data if API fails
                setGalleryItems(sampleGalleryItems);
            }
        } catch (error) {
            console.error('Failed to load gallery data:', error);
            // Use sample data as fallback
            setGalleryItems(sampleGalleryItems);
        } finally {
            setIsLoadingGallery(false);
        }
    };

    // Load gallery data on component mount
    useEffect(() => {
        loadGalleryData();
    }, []);

    // Filter gallery items by category
    const filteredItems = selectedCategory === 'all'
        ? galleryItems
        : galleryItems.filter(item => item.category === selectedCategory);

    const totalImages = galleryItems.filter(item => item.file_type === 'image').length;
    const totalVideos = galleryItems.filter(item => item.file_type === 'video').length;
    const totalViews = galleryItems.reduce((sum, item) => sum + item.views, 0);

    // Image compression function
    const compressImage = (file: File, maxWidth: number = 1920, maxHeight: number = 1080, quality: number = 0.8): Promise<File> => {
        return new Promise((resolve) => {
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            const img = new (window as any).Image();

            // Determine output format based on original file type
            const originalType = file.type;
            const isPng = originalType === 'image/png';
            const outputType = isPng ? 'image/png' : 'image/jpeg';

            img.onload = () => {
                // Calculate dimensions maintaining aspect ratio
                const { width, height } = calculateDimensions(img.width, img.height, maxWidth, maxHeight);

                canvas.width = width;
                canvas.height = height;

                // Draw and compress
                ctx?.drawImage(img, 0, 0, width, height);

                canvas.toBlob((blob) => {
                    if (blob) {
                        const compressedFile = new File([blob], file.name, {
                            type: outputType,
                            lastModified: Date.now()
                        });
                        resolve(compressedFile);
                    } else {
                        resolve(file);
                    }
                }, outputType, isPng ? 1.0 : quality); // PNG uses lossless compression
            };

            img.src = URL.createObjectURL(file);
        });
    };

    // Helper function to calculate dimensions
    const calculateDimensions = (originalWidth: number, originalHeight: number, maxWidth: number, maxHeight: number) => {
        const aspectRatio = originalWidth / originalHeight;

        let width = originalWidth;
        let height = originalHeight;

        if (width > maxWidth) {
            width = maxWidth;
            height = width / aspectRatio;
        }

        if (height > maxHeight) {
            height = maxHeight;
            width = height * aspectRatio;
        }

        return { width: Math.round(width), height: Math.round(height) };
    };

    // Upload functions
    const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
        const file = e.target.files?.[0];
        if (file) {
            try {
                toast.loading('Compressing image...');

                const compressedFile = await compressImage(file);

                const originalSizeMB = (file.size / 1024 / 1024).toFixed(2);
                const compressedSizeMB = (compressedFile.size / 1024 / 1024).toFixed(2);

                toast.dismiss();
                toast.success(`Image compressed: ${originalSizeMB}MB → ${compressedSizeMB}MB`);

                setImageFile(compressedFile);

                const reader = new FileReader();
                reader.onload = (e) => {
                    const result = e.target?.result as string;
                    setImagePreview(result);
                };
                reader.readAsDataURL(compressedFile);
            } catch (error) {
                toast.dismiss();
                toast.error('Failed to process image');
                console.error('Image compression error:', error);
            }
        }
    };

    const handleUploadSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        console.log('Form submitted!');
        setValidationErrors({});

        try {
            // Validate form
            console.log('Validating form with schema...');
            console.log('Form values:', uploadForm);
            uploadFormSchema.parse(uploadForm);
            console.log('Form validation passed!');

            if (!imageFile) {
                console.log('No image file selected');
                toast.error('Please select an image to upload');
                return;
            }
            console.log('Image file exists:', imageFile.name);

            setIsUploading(true);

            // Upload image
            const formData = new FormData();
            formData.append('image', imageFile);
            formData.append('section', 'gallery');
            formData.append('width', '1920');
            formData.append('height', '1080');
            formData.append('quality', '80');

            const uploadResponse = await api.post('/cms/upload-image', formData, {
                headers: { 'Content-Type': 'multipart/form-data' }
            });

            if (uploadResponse.data.success) {
                // Save to gallery database
                const galleryData = {
                    title: uploadForm.title,
                    description: uploadForm.description,
                    file_url: uploadResponse.data.data.url,
                    file_type: 'image',
                    category: uploadForm.category,
                    zone: uploadForm.zone
                };

                const galleryResponse = await api.post('/cms/gallery', galleryData);

                if (galleryResponse.data.success) {
                    toast.success('Image uploaded and saved to gallery successfully!');

                    // Reset form
                    setUploadForm({
                        title: '',
                        description: '',
                        category: '',
                        zone: ''
                    });
                    setImageFile(null);
                    setImagePreview('');
                    setIsUploadModalOpen(false);

                    // Reload gallery data to show new item
                    loadGalleryData();
                } else {
                    throw new Error(galleryResponse.data.message);
                }
            } else {
                throw new Error(uploadResponse.data.message);
            }
        } catch (error: any) {
            console.error('Upload error:', error);
            if (error instanceof z.ZodError) {
                console.log('Validation errors:', error.errors);
                const errors: Record<string, string> = {};
                if (error.errors && Array.isArray(error.errors)) {
                    error.errors.forEach((err) => {
                        if (err.path && Array.isArray(err.path) && err.path.length > 0) {
                            errors[err.path[0] as string] = err.message;
                        }
                    });
                }
                console.log('Setting validation errors:', errors);
                setValidationErrors(errors);
            } else {
                console.log('API error:', error.response?.data);
                toast.error(error.response?.data?.message || 'Failed to upload image');
            }
        } finally {
            setIsUploading(false);
        }
    };

    const resetUploadForm = () => {
        setUploadForm({
            title: '',
            description: '',
            category: '',
            zone: ''
        });
        setImageFile(null);
        setImagePreview('');
        setValidationErrors({});
    };

    // Get CMS content with fallbacks
    const heroTitle = content?.hero?.title || 'GALERI';
    const heroSubtitle = content?.hero?.subtitle || 'Koleksi foto dan video acara Sukan Intra Kor Kesihatan DiRaja 2025';
    const heroDescription = content?.hero?.description || 'Lihat koleksi foto dan video terbaik dari acara Sukan Intra Kor Kesihatan DiRaja 2025.';
    const heroBackgroundImage = content?.hero?.background_image || '/images/default.jpg';

    return (
        <div className="min-h-screen bg-gray-50">
            {/* Hero Section */}
            <section
                className="relative min-h-screen bg-cover bg-center bg-no-repeat pt-32 2xl:pt-40 xl:pt-36 lg:pt-28 md:pt-28 sm:pt-24"
                style={{
                    backgroundImage: `linear-gradient(rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0.5)), url('${heroBackgroundImage}')`
                }}
            >
                <div className="container mx-auto px-6 2xl:px-8 xl:px-6 lg:px-4 md:px-4 sm:px-4 py-16 2xl:py-20 xl:py-18 lg:py-12 md:py-12 sm:py-8">
                    <div className="2xl:max-w-7xl xl:max-w-7xl lg:max-w-5xl md:max-w-5xl sm:max-w-4xl max-w-full mx-auto text-center">
                        <div className="inline-flex items-center justify-center w-20 h-20 2xl:w-24 2xl:h-24 xl:w-22 xl:h-22 lg:w-16 lg:h-16 md:w-18 md:h-18 sm:w-16 sm:h-16 bg-yellow-500/20 rounded-full mb-6 2xl:mb-8 xl:mb-7 lg:mb-4 md:mb-5 sm:mb-4">
                            <Camera className="w-10 h-10 2xl:w-12 2xl:h-12 xl:w-11 xl:h-11 lg:w-8 lg:h-8 md:w-9 md:h-9 sm:w-8 sm:h-8 text-yellow-400" />
                        </div>
                        <h1 className="text-5xl md:text-6xl 2xl:text-8xl xl:text-7xl lg:text-4xl sm:text-4xl font-black text-white mb-6 2xl:mb-8 xl:mb-7 lg:mb-4 md:mb-5 sm:mb-4 drop-shadow-lg">
                            <span className="text-yellow-400">{heroTitle}</span>
                        </h1>
                        <p className="text-xl 2xl:text-2xl xl:text-xl lg:text-base md:text-base sm:text-sm text-white/90 mb-6 2xl:mb-8 xl:mb-7 lg:mb-4 md:mb-5 sm:mb-4 leading-relaxed max-w-3xl mx-auto">
                            {heroSubtitle}
                        </p>
                        <p className="text-lg 2xl:text-xl xl:text-lg lg:text-sm md:text-base sm:text-sm text-white/80 mb-8 2xl:mb-12 xl:mb-10 lg:mb-6 md:mb-8 sm:mb-6 leading-relaxed max-w-4xl mx-auto">
                            {heroDescription}
                        </p>

                        {/* Upload Button - Only show if authenticated */}
                        {console.log('Button render check:', { isAuthenticated, user, authLoading })}
                        {(isAuthenticated || (user && localStorage.getItem('auth_token'))) && (
                            <div className="flex justify-center">
                                <button
                                    className="bg-yellow-500 hover:bg-yellow-600 text-black font-bold px-8 py-3 rounded-full transition-all duration-300 hover:scale-105 shadow-lg flex items-center"
                                    onClick={(e) => {
                                        e.preventDefault();
                                        resetUploadForm();
                                        setIsUploadModalOpen(true);
                                    }}
                                >
                                    <Upload className="w-5 h-5 mr-2" />
                                    Upload Gambar
                                </button>
                            </div>
                        )}

                    </div>
                </div>
            </section>

            {/* Gallery Grid Section */}
            <section className="py-20 2xl:py-28 xl:py-24 lg:py-12 md:py-16 sm:py-12 bg-gradient-to-br from-gray-900 to-black">
                <div className="container mx-auto px-6 2xl:px-8 xl:px-6 lg:px-4 md:px-4 sm:px-4 2xl:max-w-7xl xl:max-w-7xl lg:max-w-5xl md:max-w-5xl sm:max-w-4xl max-w-full">
                    <div className="text-center mb-16 2xl:mb-20 xl:mb-18 lg:mb-8 md:mb-12 sm:mb-8">
                        <h2 className="text-5xl 2xl:text-7xl xl:text-6xl lg:text-3xl md:text-4xl sm:text-3xl font-black text-white mb-6 2xl:mb-8 xl:mb-7 lg:mb-4 md:mb-5 sm:mb-4">
                            <span className="text-yellow-400">KOLEKSI</span> MEDIA
                        </h2>
                        <p className="text-xl 2xl:text-2xl xl:text-xl lg:text-sm md:text-base sm:text-sm text-gray-300 max-w-3xl mx-auto">
                            {filteredItems.length} item tersedia
                        </p>
                    </div>

                    {isLoadingGallery ? (
                        <div className="flex justify-center items-center py-20">
                            <Loader2 className="w-8 h-8 animate-spin text-yellow-400" />
                            <span className="ml-2 text-gray-300">Loading gallery...</span>
                        </div>
                    ) : (
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-2 xl:grid-cols-3 2xl:grid-cols-4 gap-8 2xl:gap-10 xl:gap-8 lg:gap-6 md:gap-6 sm:gap-4">
                            {filteredItems.map(item => (
                                <div key={item.id} className="group bg-black/60 backdrop-blur-xl rounded-3xl 2xl:rounded-3xl xl:rounded-2xl lg:rounded-2xl md:rounded-xl sm:rounded-lg border-2 border-yellow-400/30 overflow-hidden shadow-2xl hover:border-yellow-400/60 transition-all duration-300 hover:scale-105">
                                    <div className="relative overflow-hidden">
                                        <OptimizedImage
                                            src={item.file_url}
                                            alt={item.title}
                                            className="w-full h-64 2xl:h-80 xl:h-72 lg:h-64 md:h-56 sm:h-48 object-cover group-hover:scale-110 transition-transform duration-500"
                                            lazy={true}
                                            enableCache={true}
                                        />
                                        <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-colors duration-300"></div>

                                        {/* Media Type Badge */}
                                        <div className="absolute top-4 2xl:top-6 xl:top-5 lg:top-4 md:top-3 sm:top-2 left-4 2xl:left-6 xl:left-5 lg:left-4 md:left-3 sm:left-2">
                                            <span className={`px-3 py-1 2xl:px-4 2xl:py-2 xl:px-3 xl:py-1 lg:px-3 lg:py-1 md:px-2 md:py-1 sm:px-2 sm:py-1 rounded-full text-sm 2xl:text-base xl:text-sm lg:text-xs md:text-xs sm:text-xs font-semibold border ${
                                                item.file_type === 'video'
                                                    ? 'bg-red-500/20 text-red-300 border-red-500/30'
                                                    : 'bg-blue-500/20 text-blue-300 border-blue-500/30'
                                            }`}>
                                                {item.file_type === 'video' ? (
                                                    <>
                                                        <Video className="h-3 w-3 2xl:h-4 2xl:w-4 xl:h-3 xl:w-3 lg:h-3 lg:w-3 md:h-2 md:w-2 sm:h-2 sm:w-2 inline mr-1" />
                                                        <span className="hidden sm:inline">Video</span>
                                                    </>
                                                ) : (
                                                    <>
                                                        <Image className="h-3 w-3 2xl:h-4 2xl:w-4 xl:h-3 xl:w-3 lg:h-3 lg:w-3 md:h-2 md:w-2 sm:h-2 sm:w-2 inline mr-1" />
                                                        <span className="hidden sm:inline">Foto</span>
                                                    </>
                                                )}
                                            </span>
                                        </div>

                                        {/* Category Badge */}
                                        <div className="absolute top-4 2xl:top-6 xl:top-5 lg:top-4 md:top-3 sm:top-2 right-4 2xl:right-6 xl:right-5 lg:right-4 md:right-3 sm:right-2">
                                            <span className={`px-3 py-1 2xl:px-4 2xl:py-2 xl:px-3 xl:py-1 lg:px-3 lg:py-1 md:px-2 md:py-1 sm:px-2 sm:py-1 rounded-full text-sm 2xl:text-base xl:text-sm lg:text-xs md:text-xs sm:text-xs font-semibold border ${getCategoryColor(item.category)}`}>
                                                <span className="hidden md:inline">{getCategoryName(item.category)}</span>
                                                <span className="md:hidden">🏆</span>
                                            </span>
                                        </div>

                                        {/* Play Button for Videos */}
                                        {item.file_type === 'video' && (
                                            <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                                                <div className="bg-white/90 backdrop-blur-sm rounded-full p-4 2xl:p-6 xl:p-5 lg:p-4 md:p-3 sm:p-2 hover:bg-white transition-colors duration-300">
                                                    <Play className="h-8 w-8 2xl:h-12 2xl:w-12 xl:h-10 xl:w-10 lg:h-8 lg:w-8 md:h-6 md:w-6 sm:h-4 sm:w-4 text-red-600" />
                                                </div>
                                            </div>
                                        )}

                                        {/* Zone Badge */}
                                        <div className="absolute bottom-4 2xl:bottom-6 xl:bottom-5 lg:bottom-4 md:bottom-3 sm:bottom-2 left-4 2xl:left-6 xl:left-5 lg:left-4 md:left-3 sm:left-2">
                                            <span className="px-3 py-1 2xl:px-4 2xl:py-2 xl:px-3 xl:py-1 lg:px-3 lg:py-1 md:px-2 md:py-1 sm:px-2 sm:py-1 rounded-full text-sm 2xl:text-base xl:text-sm lg:text-xs md:text-xs sm:text-xs font-semibold bg-yellow-500/20 text-yellow-300 border border-yellow-500/30">
                                                {item.zone}
                                            </span>
                                        </div>
                                    </div>

                                    <div className="p-6 2xl:p-8 xl:p-7 lg:p-6 md:p-5 sm:p-4">
                                        <h3 className="font-bold text-xl 2xl:text-2xl xl:text-xl lg:text-lg md:text-base sm:text-sm text-white mb-3 2xl:mb-4 xl:mb-3 lg:mb-3 md:mb-2 sm:mb-2 group-hover:text-yellow-400 transition-colors line-clamp-2">
                                            {item.title}
                                        </h3>
                                        <p className="text-gray-300 text-sm 2xl:text-base xl:text-sm lg:text-xs md:text-xs sm:text-xs mb-4 2xl:mb-6 xl:mb-5 lg:mb-4 md:mb-3 sm:mb-2 line-clamp-2">
                                            {item.description}
                                        </p>

                                        <div className="flex items-center justify-between text-sm 2xl:text-base xl:text-sm lg:text-xs md:text-xs sm:text-xs text-gray-400 mb-4 2xl:mb-6 xl:mb-5 lg:mb-4 md:mb-3 sm:mb-2">
                                            <div className="flex items-center space-x-1">
                                                <Calendar className="h-4 w-4 2xl:h-5 2xl:w-5 xl:h-4 xl:w-4 lg:h-3 lg:w-3 md:h-3 md:w-3 sm:h-2 sm:w-2" />
                                                <span className="hidden md:inline">{formatDate(item.uploaded_at)}</span>
                                                <span className="md:hidden">📅</span>
                                            </div>
                                            <div className="flex items-center space-x-1">
                                                <Eye className="h-4 w-4 2xl:h-5 2xl:w-5 xl:h-4 xl:w-4 lg:h-3 lg:w-3 md:h-3 md:w-3 sm:h-2 sm:w-2" />
                                                <span>{item.views.toLocaleString()}</span>
                                            </div>
                                        </div>

                                        {/* Action Buttons */}
                                        <div className="flex items-center space-x-2 2xl:space-x-3 xl:space-x-2 lg:space-x-2 md:space-x-1 sm:space-x-1">
                                            <button className="flex-1 bg-yellow-500/20 hover:bg-yellow-500/30 text-yellow-300 px-4 py-2 2xl:px-6 2xl:py-3 xl:px-5 xl:py-2 lg:px-4 lg:py-2 md:px-3 md:py-1 sm:px-2 sm:py-1 rounded-lg 2xl:rounded-xl xl:rounded-lg lg:rounded-lg md:rounded-md sm:rounded-md text-sm 2xl:text-base xl:text-sm lg:text-xs md:text-xs sm:text-xs font-semibold transition-all duration-300 border border-yellow-500/30 hover:border-yellow-500/50">
                                                <Eye className="h-4 w-4 2xl:h-5 2xl:w-5 xl:h-4 xl:w-4 lg:h-3 lg:w-3 md:h-3 md:w-3 sm:h-2 sm:w-2 inline mr-1" />
                                                <span className="hidden sm:inline">Lihat</span>
                                            </button>
                                            <button className="bg-blue-500/20 hover:bg-blue-500/30 text-blue-300 px-3 py-2 2xl:px-4 2xl:py-3 xl:px-3 xl:py-2 lg:px-3 lg:py-2 md:px-2 md:py-1 sm:px-2 sm:py-1 rounded-lg 2xl:rounded-xl xl:rounded-lg lg:rounded-lg md:rounded-md sm:rounded-md text-sm 2xl:text-base xl:text-sm lg:text-xs md:text-xs sm:text-xs transition-all duration-300 border border-blue-500/30 hover:border-blue-500/50">
                                                <Download className="h-4 w-4 2xl:h-5 2xl:w-5 xl:h-4 xl:w-4 lg:h-3 lg:w-3 md:h-3 md:w-3 sm:h-2 sm:w-2" />
                                            </button>
                                            <button className="bg-green-500/20 hover:bg-green-500/30 text-green-300 px-3 py-2 2xl:px-4 2xl:py-3 xl:px-3 xl:py-2 lg:px-3 lg:py-2 md:px-2 md:py-1 sm:px-2 sm:py-1 rounded-lg 2xl:rounded-xl xl:rounded-lg lg:rounded-lg md:rounded-md sm:rounded-md text-sm 2xl:text-base xl:text-sm lg:text-xs md:text-xs sm:text-xs transition-all duration-300 border border-green-500/30 hover:border-green-500/50">
                                                <Share2 className="h-4 w-4 2xl:h-5 2xl:w-5 xl:h-4 xl:w-4 lg:h-3 lg:w-3 md:h-3 md:w-3 sm:h-2 sm:w-2" />
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            ))}
                        </div>
                    )}
                </div>
            </section>

            {/* Upload Modal */}
            {isUploadModalOpen && (
                <div className="fixed inset-0 bg-black/80 backdrop-blur-sm z-50 flex items-center justify-center p-4">
                    <div className="bg-white rounded-2xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
                        <div className="p-6 border-b">
                            <div className="flex items-center justify-between">
                                <h2 className="text-2xl font-bold text-gray-900">
                                    Upload Gambar ke Gallery
                                </h2>
                                <button
                                    onClick={() => setIsUploadModalOpen(false)}
                                    className="text-gray-400 hover:text-gray-600 transition-colors"
                                >
                                    <X className="w-6 h-6" />
                                </button>
                            </div>
                        </div>

                        <div className="p-6">
                            <form onSubmit={handleUploadSubmit} className="space-y-6">
                                {/* Image Upload */}
                                <div className="space-y-4">
                                    <div className="space-y-2">
                                        <label htmlFor="image-file" className="block text-sm font-medium text-gray-700">
                                            Upload Gambar
                                        </label>
                                        <input
                                            id="image-file"
                                            type="file"
                                            accept="image/*"
                                            onChange={handleFileChange}
                                            className="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-yellow-50 file:text-yellow-700 hover:file:bg-yellow-100 cursor-pointer"
                                        />
                                        <p className="text-sm text-gray-500">
                                            Recommended: 1920x1080px atau lebih besar. Gambar akan dikompress secara automatik.
                                        </p>
                                    </div>

                                    {imagePreview && (
                                        <div className="space-y-2">
                                            <label className="block text-sm font-medium text-gray-700">Preview</label>
                                            <div className="relative">
                                                <img
                                                    src={imagePreview}
                                                    alt="Preview"
                                                    className="w-full h-48 object-cover rounded-lg border"
                                                />
                                                <button
                                                    type="button"
                                                    className="absolute top-2 right-2 bg-red-500 hover:bg-red-600 text-white p-1 rounded-full transition-colors"
                                                    onClick={() => {
                                                        setImageFile(null);
                                                        setImagePreview('');
                                                    }}
                                                >
                                                    <X className="w-4 h-4" />
                                                </button>
                                            </div>
                                        </div>
                                    )}
                        </div>

                                {/* Form Fields */}
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <div className="space-y-2">
                                        <label htmlFor="title" className="block text-sm font-medium text-gray-700">Title</label>
                                        <input
                                            id="title"
                                            type="text"
                                            value={uploadForm.title}
                                            onChange={(e) => setUploadForm(prev => ({ ...prev, title: e.target.value }))}
                                            placeholder="Masukkan title gambar"
                                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-yellow-500 focus:border-transparent"
                                        />
                                        {validationErrors.title && (
                                            <p className="text-sm text-red-500">{validationErrors.title}</p>
                                        )}
                                    </div>

                                    <div className="space-y-2">
                                        <label htmlFor="category" className="block text-sm font-medium text-gray-700">Category</label>
                                        <select
                                            id="category"
                                            value={uploadForm.category}
                                            onChange={(e) => setUploadForm(prev => ({ ...prev, category: e.target.value }))}
                                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-yellow-500 focus:border-transparent"
                                        >
                                            <option value="">Pilih category</option>
                                            <option value="ceremony">Majlis</option>
                                            <option value="football">Bola Sepak</option>
                                            <option value="netball">Bola Jaring</option>
                                            <option value="relay">Larian</option>
                                            <option value="athletics">Olahraga</option>
                                            <option value="general">Umum</option>
                                        </select>
                                        {validationErrors.category && (
                                            <p className="text-sm text-red-500">{validationErrors.category}</p>
                                        )}
                                    </div>
                                </div>

                                <div className="space-y-2">
                                    <label htmlFor="description" className="block text-sm font-medium text-gray-700">Description</label>
                                    <textarea
                                        id="description"
                                        value={uploadForm.description}
                                        onChange={(e) => setUploadForm(prev => ({ ...prev, description: e.target.value }))}
                                        placeholder="Masukkan description gambar"
                                        rows={3}
                                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-yellow-500 focus:border-transparent resize-none"
                                    />
                                    {validationErrors.description && (
                                        <p className="text-sm text-red-500">{validationErrors.description}</p>
                                    )}
                                </div>

                                <div className="space-y-2">
                                    <label htmlFor="zone" className="block text-sm font-medium text-gray-700">Zone</label>
                                    {user?.role === 'zone' ? (
                                        <input
                                            type="text"
                                            value={user.zone || ''}
                                            disabled
                                            className="w-full px-3 py-2 border border-gray-300 rounded-lg bg-gray-100 text-gray-600"
                                        />
                                    ) : (
                                        <select
                                            id="zone"
                                            value={uploadForm.zone}
                                            onChange={(e) => setUploadForm(prev => ({ ...prev, zone: e.target.value }))}
                                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-yellow-500 focus:border-transparent"
                                        >
                                            <option value="">Pilih zone</option>
                                            <option value="Semua Zone">Semua Zone</option>
                                            <option value="Zone A">Zone A</option>
                                            <option value="Zone B">Zone B</option>
                                            <option value="Zone C">Zone C</option>
                                            <option value="BAKAT-KKD">Bakat KKD</option>
                                        </select>
                                    )}
                                    {validationErrors.zone && (
                                        <p className="text-sm text-red-500">{validationErrors.zone}</p>
                                    )}
                                </div>

                                {/* Action Buttons */}
                                <div className="flex justify-end gap-3 pt-4">
                                    <button
                                        type="button"
                                        onClick={() => setIsUploadModalOpen(false)}
                                        disabled={isUploading}
                                        className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors disabled:opacity-50"
                                    >
                                        Batal
                                    </button>
                                    <button
                                        type="submit"
                                        disabled={!imageFile || isUploading}
                                        className="px-4 py-2 bg-yellow-500 hover:bg-yellow-600 text-black font-semibold rounded-lg transition-colors disabled:opacity-50 flex items-center"
                                    >
                                        {isUploading ? (
                                            <>
                                                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                                                Uploading...
                                            </>
                                        ) : (
                                            <>
                                                <Upload className="w-4 h-4 mr-2" />
                                                Upload
                                            </>
                                        )}
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            )}

        </div>
    );
}