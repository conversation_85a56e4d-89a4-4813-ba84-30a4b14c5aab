import { useState, useEffect } from 'react';
import { Trophy, Medal, Award, Target, Users, Star, Crown } from 'lucide-react';
import { usePageContent } from '@/hooks/usePageContent';
import publicApi from '@/lib/public-api';

// Types for standings data
interface StandingData {
    position: number;
    zone: string;
    points: number;
    gold: number;
    silver: number;
    bronze: number;
}

export default function OverallStandings() {
    const { data: content = {}, isLoading: loading } = usePageContent('overall-standings');
    const [standings, setStandings] = useState<StandingData[]>([]);
    const [isLoading, setIsLoading] = useState(true);

    // Get CMS content with fallbacks
    const heroTitle = content?.hero?.title || 'KEDUDUKAN MATA KESELURUHAN';
    const heroSubtitle = content?.hero?.subtitle || 'Kedudukan terkini semua zon dalam Sukan Intra Kor Kesihatan DiRaja 2025';
    const heroDescription = content?.hero?.description || 'Pantau kedudukan mata keseluruhan setiap zon dan lihat siapa yang mendahului dalam pertandingan tahun ini.';
    const heroBackgroundImage = content?.hero?.background_image || '/images/default.jpg';

    // Fetch standings data from API
    const fetchStandings = async () => {
        try {
            setIsLoading(true);

            const response = await publicApi.get('/public/jadual/overall-standings');
            setStandings(response.data.data || []);

        } catch (error) {
            console.error('Error fetching standings data:', error);
            setStandings([]);
        } finally {
            setIsLoading(false);
        }
    };

    useEffect(() => {
        fetchStandings();
    }, []);

    // Helper function to get position styling
    const getPositionStyling = (position: number) => {
        switch (position) {
            case 1:
                return {
                    color: 'text-yellow-400',
                    bgColor: 'bg-gradient-to-r from-yellow-500/20 to-orange-500/20',
                    borderColor: 'border-yellow-400/50'
                };
            case 2:
                return {
                    color: 'text-gray-300',
                    bgColor: 'bg-gradient-to-r from-gray-400/20 to-gray-500/20',
                    borderColor: 'border-gray-400/50'
                };
            case 3:
                return {
                    color: 'text-orange-400',
                    bgColor: 'bg-gradient-to-r from-amber-600/20 to-amber-700/20',
                    borderColor: 'border-amber-600/50'
                };
            default:
                return {
                    color: 'text-blue-400',
                    bgColor: 'bg-white/10',
                    borderColor: 'border-white/20'
                };
        }
    };

    if (loading || isLoading) {
        return (
            <div className="min-h-screen bg-gray-900 flex items-center justify-center">
                <div className="text-white text-xl">Loading...</div>
            </div>
        );
    }

    return (
        <div className="min-h-screen bg-gray-50">
            {/* Hero Section */}
            <section
                className="relative min-h-screen bg-cover bg-center bg-no-repeat pt-32 2xl:pt-40 xl:pt-36 lg:pt-28 md:pt-28 sm:pt-24"
                style={{
                    backgroundImage: `linear-gradient(rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0.5)), url('${heroBackgroundImage}')`
                }}
            >
                <div className="container mx-auto px-6 2xl:px-8 xl:px-6 lg:px-4 md:px-4 sm:px-4 py-16 2xl:py-20 xl:py-18 lg:py-12 md:py-12 sm:py-8">
                    <div className="2xl:max-w-7xl xl:max-w-7xl lg:max-w-5xl md:max-w-5xl sm:max-w-4xl max-w-full mx-auto text-center">
                        <div className="inline-flex items-center justify-center w-20 h-20 2xl:w-24 2xl:h-24 xl:w-22 xl:h-22 lg:w-16 lg:h-16 md:w-18 md:h-18 sm:w-14 sm:h-14 bg-yellow-500/20 rounded-full mb-6 2xl:mb-8 xl:mb-7 lg:mb-4 md:mb-5 sm:mb-4">
                            <Trophy className="w-10 h-10 2xl:w-12 2xl:h-12 xl:w-11 xl:h-11 lg:w-8 lg:h-8 md:w-9 md:h-9 sm:w-7 sm:h-7 text-yellow-400" />
                        </div>
                        <h1 className="text-4xl md:text-6xl 2xl:text-8xl xl:text-7xl lg:text-4xl sm:text-2xl font-black text-white mb-4 2xl:mb-8 xl:mb-7 lg:mb-4 md:mb-5 sm:mb-3 drop-shadow-lg">
                            <span className="text-yellow-400">{heroTitle}</span>
                        </h1>
                        <p className="text-lg 2xl:text-2xl xl:text-xl lg:text-sm md:text-base sm:text-xs text-white/90 mb-4 2xl:mb-6 xl:mb-5 lg:mb-3 md:mb-4 sm:mb-3 leading-relaxed 2xl:max-w-4xl xl:max-w-4xl lg:max-w-3xl md:max-w-3xl sm:max-w-full max-w-full mx-auto px-4 sm:px-6">
                            {heroSubtitle}
                        </p>
                        <p className="text-base 2xl:text-lg xl:text-base lg:text-xs md:text-sm sm:text-xs text-white/80 mb-6 2xl:mb-10 xl:mb-9 lg:mb-6 md:mb-7 sm:mb-4 leading-relaxed 2xl:max-w-5xl xl:max-w-5xl lg:max-w-4xl md:max-w-4xl sm:max-w-full max-w-full mx-auto px-4 sm:px-6">
                            {heroDescription}
                        </p>
                    </div>
                </div>
            </section>

            {/* Standings Section */}
            <section className="py-16 2xl:py-28 xl:py-24 lg:py-12 md:py-16 sm:py-6 bg-gradient-to-br from-gray-900 to-black">
                <div className="container mx-auto px-4 2xl:px-8 xl:px-6 lg:px-4 md:px-4 sm:px-4 2xl:max-w-7xl xl:max-w-7xl lg:max-w-5xl md:max-w-5xl sm:max-w-full max-w-full">
                    <div className="text-center mb-12 2xl:mb-20 xl:mb-18 lg:mb-8 md:mb-12 sm:mb-4">
                        <h2 className="text-4xl 2xl:text-7xl xl:text-6xl lg:text-3xl md:text-4xl sm:text-xl font-black text-white mb-4 2xl:mb-8 xl:mb-7 lg:mb-4 md:mb-5 sm:mb-2">
                            <span className="text-yellow-400">KEDUDUKAN</span> SEMASA
                        </h2>
                        <p className="text-lg 2xl:text-2xl xl:text-xl lg:text-sm md:text-base sm:text-xs text-gray-300 max-w-3xl mx-auto px-4">
                            Kedudukan terkini berdasarkan mata terkumpul
                        </p>
                    </div>

                    <div className="space-y-6 2xl:space-y-8 xl:space-y-7 lg:space-y-4 md:space-y-5 sm:space-y-3 2xl:max-w-5xl xl:max-w-5xl lg:max-w-4xl md:max-w-4xl sm:max-w-full max-w-full mx-auto">
                        {standings.length === 0 ? (
                            <div className="text-center py-12">
                                <Trophy className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                                <p className="text-gray-400 text-lg">Kedudukan akan dikemaskini selepas pertandingan selesai</p>
                            </div>
                        ) : (
                            standings.map((standing) => {
                                const styling = getPositionStyling(standing.position);
                                return (
                                    <div key={standing.position} className={`bg-black/60 backdrop-blur-xl rounded-2xl 2xl:rounded-3xl xl:rounded-2xl lg:rounded-2xl md:rounded-xl sm:rounded-lg border-2 ${styling.borderColor} overflow-hidden shadow-2xl hover:border-opacity-70 transition-all duration-300`}>
                                        <div className={`${styling.bgColor} backdrop-blur-sm p-6 2xl:p-10 xl:p-9 lg:p-6 md:p-7 sm:p-4`}>
                                            <div className="flex items-center justify-between flex-col sm:flex-col lg:flex-row gap-4 sm:gap-4 lg:gap-0">
                                                <div className="flex items-center space-x-4 2xl:space-x-6 xl:space-x-5 lg:space-x-4 md:space-x-5 sm:space-x-3">
                                                    <div className="flex items-center space-x-2 sm:space-x-3 lg:space-x-2 xl:space-x-3">
                                                        {standing.position === 1 && <Crown className="h-8 w-8 sm:h-9 sm:w-9 lg:h-8 lg:w-8 xl:h-10 xl:w-10 text-yellow-400" />}
                                                        {standing.position === 2 && <Medal className="h-8 w-8 sm:h-9 sm:w-9 lg:h-8 lg:w-8 xl:h-10 xl:w-10 text-gray-400" />}
                                                        {standing.position === 3 && <Medal className="h-8 w-8 sm:h-9 sm:w-9 lg:h-8 lg:w-8 xl:h-10 xl:w-10 text-amber-600" />}
                                                        {standing.position > 3 && <Star className="h-8 w-8 sm:h-9 sm:w-9 lg:h-8 lg:w-8 xl:h-10 xl:w-10 text-gray-400" />}
                                                        <span className="text-2xl sm:text-3xl lg:text-2xl xl:text-4xl font-black text-white">#{standing.position}</span>
                                                    </div>
                                                    <div>
                                                        <h3 className="text-3xl 2xl:text-5xl xl:text-4xl lg:text-2xl md:text-3xl sm:text-xl font-black text-white">
                                                            {standing.zone}
                                                        </h3>
                                                        <p className="text-lg 2xl:text-2xl xl:text-xl lg:text-sm md:text-base sm:text-xs text-white/80">
                                                            {standing.points} Mata
                                                        </p>
                                                    </div>
                                                </div>
                                        
                                                <div className="flex items-center justify-center sm:justify-end space-x-4 sm:space-x-6 lg:space-x-4 xl:space-x-8">
                                                    <div className="text-center">
                                                        <div className="flex items-center space-x-1 sm:space-x-2 lg:space-x-1 xl:space-x-2">
                                                            <Medal className="h-4 w-4 sm:h-5 sm:w-5 lg:h-4 lg:w-4 xl:h-6 xl:w-6 text-yellow-400" />
                                                            <span className="text-lg sm:text-xl lg:text-lg xl:text-2xl font-bold text-yellow-400">{standing.gold}</span>
                                                        </div>
                                                        <p className="text-xs sm:text-sm lg:text-xs xl:text-sm text-white/70">Emas</p>
                                                    </div>
                                                    <div className="text-center">
                                                        <div className="flex items-center space-x-1 sm:space-x-2 lg:space-x-1 xl:space-x-2">
                                                            <Medal className="h-4 w-4 sm:h-5 sm:w-5 lg:h-4 lg:w-4 xl:h-6 xl:w-6 text-gray-400" />
                                                            <span className="text-lg sm:text-xl lg:text-lg xl:text-2xl font-bold text-gray-400">{standing.silver}</span>
                                                        </div>
                                                        <p className="text-xs sm:text-sm lg:text-xs xl:text-sm text-white/70">Perak</p>
                                                    </div>
                                                    <div className="text-center">
                                                        <div className="flex items-center space-x-1 sm:space-x-2 lg:space-x-1 xl:space-x-2">
                                                            <Medal className="h-4 w-4 sm:h-5 sm:w-5 lg:h-4 lg:w-4 xl:h-6 xl:w-6 text-amber-600" />
                                                            <span className="text-lg sm:text-xl lg:text-lg xl:text-2xl font-bold text-amber-600">{standing.bronze}</span>
                                                        </div>
                                                        <p className="text-xs sm:text-sm lg:text-xs xl:text-sm text-white/70">Gangsa</p>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                );
                            })
                        )}
                        ))}
                    </div>
                </div>
            </section>

            {/* Coming Soon Notice */}
            <section className="py-20 2xl:py-28 xl:py-24 lg:py-12 md:py-16 sm:py-8 bg-gradient-to-br from-black to-gray-900">
                <div className="container mx-auto px-6 2xl:px-8 xl:px-6 lg:px-4 md:px-4 sm:px-4 2xl:max-w-7xl xl:max-w-7xl lg:max-w-5xl md:max-w-5xl sm:max-w-4xl max-w-full">
                    <div className="2xl:max-w-5xl xl:max-w-5xl lg:max-w-4xl md:max-w-4xl sm:max-w-3xl max-w-full mx-auto">
                        <div className="bg-gradient-to-r from-blue-600/20 to-purple-600/20 backdrop-blur-xl rounded-3xl 2xl:rounded-3xl xl:rounded-2xl lg:rounded-2xl md:rounded-xl sm:rounded-lg border-2 border-blue-400/50 p-8 2xl:p-10 xl:p-9 lg:p-6 md:p-7 sm:p-4 text-center">
                            <Star className="h-16 w-16 2xl:h-20 2xl:w-20 xl:h-18 xl:w-18 lg:h-12 lg:w-12 md:h-14 md:w-14 sm:h-10 sm:w-10 text-blue-400 mx-auto mb-6" />
                            <h3 className="text-3xl 2xl:text-4xl xl:text-3xl lg:text-2xl md:text-2xl sm:text-xl font-bold text-blue-400 mb-4">
                                Kedudukan Akan Dikemaskini
                            </h3>
                            <p className="text-lg 2xl:text-xl xl:text-lg lg:text-sm md:text-base sm:text-sm text-white/90 leading-relaxed">
                                Kedudukan mata keseluruhan akan dikemaskini secara real-time semasa pertandingan berlangsung. 
                                Pantau terus untuk melihat kedudukan terkini setiap zon.
                            </p>
                        </div>
                    </div>
                </div>
            </section>
        </div>
    );
}
