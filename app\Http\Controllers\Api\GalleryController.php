<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Gallery;
use App\Models\User;
use Illuminate\Http\Request;

class GalleryController extends Controller
{
    /**
     * Display a listing of gallery items
     */
    public function index(Request $request)
    {
        $query = Gallery::query();

        // Filter by category if specified
        if ($request->has('category')) {
            $query->where('category', $request->category);
        }

        // Filter by zone if specified
        if ($request->has('zone')) {
            $query->where('zone', $request->zone);
        }
        
        // Filter by file type
        if ($request->has('type')) {
            $query->where('file_type', $request->type);
        }
        
        // Search by title or description
        if ($request->has('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }
        
        // Filter by featured
        if ($request->has('featured')) {
            $query->where('is_featured', $request->boolean('featured'));
        }
        
        // Pagination
        $perPage = $request->get('per_page', 12);
        $gallery = $query->orderBy('created_at', 'desc')
                        ->paginate($perPage);
        
        // Transform the data
        $gallery->getCollection()->transform(function ($item) {
            $uploader = User::find($item->uploaded_by);

            return [
                'id' => $item->id,
                'title' => $item->title,
                'description' => $item->description,
                'file_url' => $item->file_url,
                'file_type' => $item->file_type,
                'category' => $item->category,
                'zone' => $item->zone,
                'is_featured' => $item->is_featured,
                'views' => $item->views,
                'uploaded_at' => $item->created_at->format('Y-m-d H:i:s'),
                'uploader' => $uploader ? $uploader->name : null,
            ];
        });

        return response()->json($gallery);
    }

    /**
     * Store a new gallery item
     */
    public function store(Request $request)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'nullable|string|max:1000',
            'file_url' => 'required|string|max:500',
            'file_type' => 'required|in:image,video',
            'category' => 'nullable|string|max:100',
            'zone' => 'required|string|max:20',
        ]);

        // Get authenticated user ID and ensure it's an integer
        $userId = auth()->id();
        $user = auth()->user();

        // Debug authentication
        \Log::info('Gallery upload - User ID: ' . $userId);
        \Log::info('Gallery upload - User: ' . json_encode($user));

        if (!is_numeric($userId)) {
            return response()->json([
                'success' => false,
                'message' => 'Invalid user authentication. User ID: ' . $userId
            ], 401);
        }

        $gallery = Gallery::create([
            'title' => $request->title,
            'description' => $request->description,
            'file_url' => $request->file_url,
            'file_type' => $request->file_type,
            'category' => $request->category,
            'zone' => $request->zone,
            'uploaded_by' => (int) $userId,
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Gallery item created successfully',
            'data' => [
                'id' => $gallery->id,
                'title' => $gallery->title,
                'description' => $gallery->description,
                'file_url' => $gallery->file_url,
                'file_type' => $gallery->file_type,
                'category' => $gallery->category,
                'zone' => $gallery->zone,
                'views' => $gallery->views,
                'is_featured' => $gallery->is_featured,
                'uploaded_at' => $gallery->created_at->format('Y-m-d H:i:s'),
            ]
        ], 201);
    }

    /**
     * Display the specified gallery item
     */
    public function show($id)
    {
        $item = Gallery::with('uploader')->findOrFail($id);

        // Increment views count
        $item->increment('views');

        return response()->json([
            'id' => $item->id,
            'title' => $item->title,
            'description' => $item->description,
            'file_url' => $item->file_url,
            'file_type' => $item->file_type,
            'category' => $item->category,
            'zone' => $item->zone,
            'is_featured' => $item->is_featured,
            'views' => $item->views,
            'uploaded_at' => $item->created_at->format('Y-m-d H:i:s'),
            'uploader' => $item->uploader ? $item->uploader->name : null,
        ]);
    }

    /**
     * Get featured gallery items
     */
    public function featured(Request $request)
    {
        $limit = $request->get('limit', 8);

        $gallery = Gallery::with('uploader')
                         ->where('is_featured', true)
                         ->orderBy('created_at', 'desc')
                         ->limit($limit)
                         ->get();

        $gallery = $gallery->map(function ($item) {
            return [
                'id' => $item->id,
                'title' => $item->title,
                'description' => $item->description,
                'file_url' => $item->file_url,
                'file_type' => $item->file_type,
                'category' => $item->category,
                'zone' => $item->zone,
                'uploaded_at' => $item->created_at->format('Y-m-d H:i:s'),
            ];
        });

        return response()->json($gallery);
    }

    /**
     * Get gallery categories
     */
    public function categories()
    {
        $categories = Gallery::distinct()
                            ->pluck('category')
                            ->filter()
                            ->values();

        return response()->json($categories);
    }

    /**
     * Get gallery items by sport
     */
    public function bySport(Request $request)
    {
        $sports = Sport::with(['galleryItems' => function ($query) use ($request) {
            $query->orderBy('created_at', 'desc');
            if ($request->has('limit')) {
                $query->limit($request->limit);
            }
        }])->get();

        $result = $sports->map(function ($sport) {
            return [
                'sport_id' => $sport->id,
                'sport_name' => $sport->name,
                'items' => $sport->galleryItems->map(function ($item) {
                    return [
                        'id' => $item->id,
                        'title' => $item->title,
                        'file_path' => $item->file_path,
                        'file_type' => $item->file_type,
                        'category' => $item->category,
                        'uploaded_at' => $item->created_at->format('Y-m-d H:i:s'),
                    ];
                })
            ];
        });

        return response()->json($result);
    }
}
