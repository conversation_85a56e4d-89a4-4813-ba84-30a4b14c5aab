import React, { useState, useEffect } from 'react';
import { Camera, Plus, Loader2 } from 'lucide-react';
import { usePageContent } from '@/hooks/usePageContent';
import { useAuth } from '@/contexts/auth-context';
import api from '@/lib/axios';
import toast from 'react-hot-toast';

// Components
import GalleryCard from '@/components/gallery/GalleryCard';
import ViewMediaModal from '@/components/gallery/ViewMediaModal';
import EditMediaModal from '@/components/gallery/EditMediaModal';
import DeleteConfirmModal from '@/components/gallery/DeleteConfirmModal';
import UploadMultipleModal from '@/components/gallery/UploadMultipleModal';

// Utils
import { downloadAllImages, shareMedia, getCategoryName } from '@/utils/galleryUtils';

interface GalleryItem {
    id: number;
    title: string;
    description: string;
    file_url: string;
    file_urls?: string[];
    image_count?: number;
    file_type: 'image' | 'video';
    category: string;
    zone: string;
    views: number;
    is_featured: boolean;
    uploaded_at: string;
    uploader?: string;
}

export default function Gallery() {
    const [selectedCategory, setSelectedCategory] = useState<string>('all');
    const { data: content = {}, isLoading: loading } = usePageContent('gallery');
    const { isAuthenticated, user, isLoading: authLoading } = useAuth();

    // Gallery data states
    const [galleryItems, setGalleryItems] = useState<GalleryItem[]>([]);
    const [isLoadingGallery, setIsLoadingGallery] = useState(true);

    // Modal states
    const [viewModalOpen, setViewModalOpen] = useState(false);
    const [editModalOpen, setEditModalOpen] = useState(false);
    const [deleteModalOpen, setDeleteModalOpen] = useState(false);
    const [uploadModalOpen, setUploadModalOpen] = useState(false);
    const [selectedMedia, setSelectedMedia] = useState<GalleryItem | null>(null);

    // Load gallery data from API
    const loadGalleryData = async () => {
        try {
            setIsLoadingGallery(true);
            const response = await api.get('/public/gallery');

            if (response.data && response.data.data) {
                setGalleryItems(response.data.data);
            }
        } catch (error) {

            toast.error('Failed to load gallery data');
        } finally {
            setIsLoadingGallery(false);
        }
    };

    // Load gallery data on component mount
    useEffect(() => {
        loadGalleryData();
    }, []);

    // Filter gallery items by category
    const filteredItems = selectedCategory === 'all'
        ? galleryItems
        : galleryItems.filter(item => item.category === selectedCategory);

    const totalImages = galleryItems.filter(item => item.file_type === 'image').length;
    const totalVideos = galleryItems.filter(item => item.file_type === 'video').length;
    const totalViews = galleryItems.reduce((sum, item) => sum + item.views, 0);

    // Get unique categories
    const categories = Array.from(new Set(galleryItems.map(item => item.category)));

    // Get CMS content with fallbacks
    const heroTitle = content?.hero?.title || 'GALERI';
    const heroSubtitle = content?.hero?.subtitle || 'Koleksi foto dan video acara Sukan Intra Kor Kesihatan DiRaja 2025';
    const heroDescription = content?.hero?.description || 'Lihat koleksi foto dan video terbaik dari acara Sukan Intra Kor Kesihatan DiRaja 2025.';
    const heroBackgroundImage = content?.hero?.background_image || '/images/default.jpg';

    // Modal handlers
    const handleView = (item: GalleryItem) => {
        setSelectedMedia(item);
        setViewModalOpen(true);
    };

    const handleEdit = (item: GalleryItem) => {
        setSelectedMedia(item);
        setEditModalOpen(true);
    };

    const handleDelete = (item: GalleryItem) => {
        setSelectedMedia(item);
        setDeleteModalOpen(true);
    };

    const handleDownload = (item: GalleryItem) => {
        downloadAllImages(item);
    };

    const handleShare = (item: GalleryItem) => {
        shareMedia(item);
    };

    const handleUploadSuccess = () => {
        loadGalleryData();
    };

    const handleEditSuccess = () => {
        loadGalleryData();
    };

    const handleDeleteSuccess = () => {
        loadGalleryData();
    };

    return (
        <div className="min-h-screen bg-gray-50">
            {/* Hero Section */}
            <section
                className="relative min-h-screen bg-cover bg-center bg-no-repeat pt-32 2xl:pt-40 xl:pt-36 lg:pt-28 md:pt-28 sm:pt-24"
                style={{
                    backgroundImage: `linear-gradient(rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0.5)), url('${heroBackgroundImage}')`
                }}
            >
                <div className="container mx-auto px-6 2xl:px-8 xl:px-6 lg:px-4 md:px-4 sm:px-4 py-16 2xl:py-20 xl:py-18 lg:py-12 md:py-12 sm:py-8">
                    <div className="2xl:max-w-7xl xl:max-w-7xl lg:max-w-5xl md:max-w-5xl sm:max-w-4xl max-w-full mx-auto text-center">
                        <div className="inline-flex items-center justify-center w-20 h-20 2xl:w-24 2xl:h-24 xl:w-22 xl:h-22 lg:w-16 lg:h-16 md:w-18 md:h-18 sm:w-16 sm:h-16 bg-yellow-500/20 rounded-full mb-6 2xl:mb-8 xl:mb-7 lg:mb-4 md:mb-5 sm:mb-4">
                            <Camera className="w-10 h-10 2xl:w-12 2xl:h-12 xl:w-11 xl:h-11 lg:w-8 lg:h-8 md:w-9 md:h-9 sm:w-8 sm:h-8 text-yellow-400" />
                        </div>

                        <h1 className="text-6xl 2xl:text-8xl xl:text-7xl lg:text-5xl md:text-4xl sm:text-3xl font-bold text-white mb-6 2xl:mb-8 xl:mb-7 lg:mb-4 md:mb-5 sm:mb-4">
                            {heroTitle}
                        </h1>
                        <p className="text-2xl 2xl:text-3xl xl:text-2xl lg:text-xl md:text-lg sm:text-base text-yellow-400 mb-4 2xl:mb-6 xl:mb-5 lg:mb-3 md:mb-4 sm:mb-3 font-semibold">
                            {heroSubtitle}
                        </p>
                        <p className="text-lg 2xl:text-xl xl:text-lg lg:text-base md:text-sm sm:text-sm text-gray-300 mb-8 2xl:mb-12 xl:mb-10 lg:mb-6 md:mb-8 sm:mb-6 max-w-3xl mx-auto leading-relaxed">
                            {heroDescription}
                        </p>

                        {/* Stats */}
                        <div className="grid grid-cols-3 gap-8 2xl:gap-12 xl:gap-10 lg:gap-6 md:gap-8 sm:gap-4 max-w-2xl mx-auto mb-12 2xl:mb-16 xl:mb-14 lg:mb-8 md:mb-12 sm:mb-8">
                            <div className="text-center">
                                <div className="text-4xl 2xl:text-6xl xl:text-5xl lg:text-3xl md:text-2xl sm:text-xl font-bold text-yellow-400 mb-2">
                                    {totalImages}
                                </div>
                                <div className="text-sm 2xl:text-lg xl:text-base lg:text-sm md:text-xs sm:text-xs text-gray-300 uppercase tracking-wider">
                                    Foto
                                </div>
                            </div>
                            <div className="text-center">
                                <div className="text-4xl 2xl:text-6xl xl:text-5xl lg:text-3xl md:text-2xl sm:text-xl font-bold text-yellow-400 mb-2">
                                    {totalVideos}
                                </div>
                                <div className="text-sm 2xl:text-lg xl:text-base lg:text-sm md:text-xs sm:text-xs text-gray-300 uppercase tracking-wider">
                                    Video
                                </div>
                            </div>
                            <div className="text-center">
                                <div className="text-4xl 2xl:text-6xl xl:text-5xl lg:text-3xl md:text-2xl sm:text-xl font-bold text-yellow-400 mb-2">
                                    {totalViews.toLocaleString()}
                                </div>
                                <div className="text-sm 2xl:text-lg xl:text-base lg:text-sm md:text-xs sm:text-xs text-gray-300 uppercase tracking-wider">
                                    Tontonan
                                </div>
                            </div>
                        </div>

                        {/* Upload Button for authenticated users */}
                        {isAuthenticated && (
                            <button
                                onClick={() => setUploadModalOpen(true)}
                                className="inline-flex items-center px-8 py-4 2xl:px-10 2xl:py-5 xl:px-9 xl:py-4 lg:px-6 lg:py-3 md:px-8 md:py-4 sm:px-6 sm:py-3 bg-yellow-500 hover:bg-yellow-600 text-black font-bold rounded-full 2xl:rounded-2xl xl:rounded-xl lg:rounded-lg md:rounded-xl sm:rounded-lg text-lg 2xl:text-xl xl:text-lg lg:text-base md:text-base sm:text-sm transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-105"
                            >
                                <Plus className="w-6 h-6 2xl:w-7 2xl:h-7 xl:w-6 xl:h-6 lg:w-5 lg:h-5 md:w-6 md:h-6 sm:w-5 sm:h-5 mr-3" />
                                Upload Media
                            </button>
                        )}
                    </div>
                </div>
            </section>

            {/* Gallery Section */}
            <section className="py-20 2xl:py-32 xl:py-28 lg:py-16 md:py-20 sm:py-12 bg-gray-900">
                <div className="container mx-auto px-6 2xl:px-8 xl:px-6 lg:px-4 md:px-4 sm:px-4">
                    {/* Category Filter */}
                    <div className="flex flex-wrap justify-center gap-4 2xl:gap-6 xl:gap-5 lg:gap-3 md:gap-4 sm:gap-2 mb-12 2xl:mb-16 xl:mb-14 lg:mb-8 md:mb-12 sm:mb-8">
                        <button
                            onClick={() => setSelectedCategory('all')}
                            className={`px-6 py-3 2xl:px-8 2xl:py-4 xl:px-7 xl:py-3 lg:px-5 lg:py-2 md:px-6 md:py-3 sm:px-4 sm:py-2 rounded-full text-sm 2xl:text-lg xl:text-base lg:text-sm md:text-sm sm:text-xs font-semibold transition-all duration-300 border-2 ${
                                selectedCategory === 'all'
                                    ? 'bg-yellow-500 text-black border-yellow-500'
                                    : 'bg-transparent text-yellow-400 border-yellow-400 hover:bg-yellow-400/10'
                            }`}
                        >
                            Semua ({galleryItems.length})
                        </button>
                        {categories.map(category => (
                            <button
                                key={category}
                                onClick={() => setSelectedCategory(category)}
                                className={`px-6 py-3 2xl:px-8 2xl:py-4 xl:px-7 xl:py-3 lg:px-5 lg:py-2 md:px-6 md:py-3 sm:px-4 sm:py-2 rounded-full text-sm 2xl:text-lg xl:text-base lg:text-sm md:text-sm sm:text-xs font-semibold transition-all duration-300 border-2 ${
                                    selectedCategory === category
                                        ? 'bg-yellow-500 text-black border-yellow-500'
                                        : 'bg-transparent text-yellow-400 border-yellow-400 hover:bg-yellow-400/10'
                                }`}
                            >
                                {getCategoryName(category)} ({galleryItems.filter(item => item.category === category).length})
                            </button>
                        ))}
                    </div>

                    {/* Gallery Grid */}
                    {isLoadingGallery ? (
                        <div className="flex justify-center items-center py-20">
                            <Loader2 className="w-8 h-8 animate-spin text-yellow-400" />
                            <span className="ml-2 text-gray-300">Loading gallery...</span>
                        </div>
                    ) : (
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-2 xl:grid-cols-3 2xl:grid-cols-4 gap-8 2xl:gap-10 xl:gap-8 lg:gap-6 md:gap-6 sm:gap-4">
                            {filteredItems.map(item => (
                                <GalleryCard
                                    key={item.id}
                                    item={item}
                                    onView={handleView}
                                    onEdit={handleEdit}
                                    onDelete={handleDelete}
                                    onDownload={handleDownload}
                                    onShare={handleShare}
                                />
                            ))}
                        </div>
                    )}

                    {/* Empty State */}
                    {!isLoadingGallery && filteredItems.length === 0 && (
                        <div className="text-center py-20">
                            <Camera className="w-16 h-16 text-gray-600 mx-auto mb-4" />
                            <h3 className="text-xl font-semibold text-gray-400 mb-2">
                                No media found
                            </h3>
                            <p className="text-gray-500">
                                {selectedCategory === 'all' 
                                    ? 'No media has been uploaded yet.' 
                                    : `No media found in ${getCategoryName(selectedCategory)} category.`
                                }
                            </p>
                        </div>
                    )}
                </div>
            </section>

            {/* Modals */}
            <ViewMediaModal
                isOpen={viewModalOpen}
                onClose={() => setViewModalOpen(false)}
                media={selectedMedia}
                onDownload={downloadAllImages}
                onShare={shareMedia}
            />

            <EditMediaModal
                isOpen={editModalOpen}
                onClose={() => setEditModalOpen(false)}
                media={selectedMedia}
                onSuccess={handleEditSuccess}
            />

            <DeleteConfirmModal
                isOpen={deleteModalOpen}
                onClose={() => setDeleteModalOpen(false)}
                media={selectedMedia}
                onSuccess={handleDeleteSuccess}
            />

            <UploadMultipleModal
                isOpen={uploadModalOpen}
                onClose={() => setUploadModalOpen(false)}
                onSuccess={handleUploadSuccess}
            />
        </div>
    );
}
