import { useState, useEffect } from 'react';
import { Search, Filter, Download, Eye, ChevronLeft, ChevronRight } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import api from '@/lib/axios';
import { toast } from 'react-hot-toast';

interface Player {
    id: number;
    name: string;
    phone: string;
    player_type: 'main' | 'substitute';
    status: string;
    zone: string | null;
    zone_name: string | null;
    sport: string | null;
    sport_id: number | null;
    team_leader_name: string | null;
    team_leader_phone: string | null;
    created_at: string;
}

interface Zone {
    code: string;
    name: string;
}

interface Sport {
    id: number;
    name: string;
}

interface FilterOptions {
    zones: Zone[];
    sports: Sport[];
}

interface PlayersListTableProps {
    onViewPlayer: (player: Player) => void;
}

export default function PlayersListTable({ onViewPlayer }: PlayersListTableProps) {
    const [players, setPlayers] = useState<Player[]>([]);
    const [loading, setLoading] = useState(true);
    const [filterOptions, setFilterOptions] = useState<FilterOptions>({ zones: [], sports: [] });
    
    // Filter states
    const [selectedZone, setSelectedZone] = useState<string>('');
    const [selectedSport, setSelectedSport] = useState<string>('');
    const [searchTerm, setSearchTerm] = useState<string>('');

    // Pagination states
    const [currentPage, setCurrentPage] = useState<number>(1);
    const [totalPages, setTotalPages] = useState<number>(1);
    const [totalPlayers, setTotalPlayers] = useState<number>(0);
    const [perPage] = useState<number>(5);

    useEffect(() => {
        fetchFilterOptions();
        fetchPlayers();
    }, []);

    useEffect(() => {
        setCurrentPage(1); // Reset to first page when filters change
        fetchPlayers();
    }, [selectedZone, selectedSport, searchTerm]);

    useEffect(() => {
        fetchPlayers();
    }, [currentPage]);

    const fetchFilterOptions = async () => {
        try {
            const response = await api.get('/admin/players/filter-options');
            setFilterOptions(response.data);
        } catch (error) {
            console.error('Error fetching filter options:', error);
            toast.error('Gagal memuat pilihan filter');
        }
    };

    const fetchPlayers = async () => {
        try {
            setLoading(true);
            const params = new URLSearchParams();

            if (selectedZone && selectedZone !== 'all') params.append('zone', selectedZone);
            if (selectedSport && selectedSport !== 'all') params.append('sport', selectedSport);
            if (searchTerm) params.append('search', searchTerm);
            params.append('page', currentPage.toString());
            params.append('per_page', perPage.toString());

            const response = await api.get(`/admin/players/all?${params}`);
            setPlayers(response.data.data || response.data.players || []);
            setTotalPages(response.data.last_page || 1);
            setTotalPlayers(response.data.total || 0);
        } catch (error) {
            console.error('Error fetching players:', error);
            toast.error('Gagal memuat data peserta');
        } finally {
            setLoading(false);
        }
    };

    const handleExport = async () => {
        try {
            const params = new URLSearchParams();
            if (selectedZone && selectedZone !== 'all') params.append('zone', selectedZone);
            if (selectedSport && selectedSport !== 'all') params.append('sport', selectedSport);
            if (searchTerm) params.append('search', searchTerm);

            // TODO: Implement Excel export
            toast.success('Export akan diimplementasikan');
        } catch (error) {
            console.error('Error exporting data:', error);
            toast.error('Gagal export data');
        }
    };

    const getStatusBadge = (status: string) => {
        const statusConfig = {
            'active': { label: 'Aktif', className: 'bg-green-100 text-green-800' },
            'inactive': { label: 'Tidak Aktif', className: 'bg-red-100 text-red-800' },
            'pending': { label: 'Menunggu', className: 'bg-yellow-100 text-yellow-800' },
        };
        
        const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.active;
        return <Badge className={config.className}>{config.label}</Badge>;
    };

    const getPlayerTypeBadge = (type: 'main' | 'substitute') => {
        return type === 'main' 
            ? <Badge variant="default">Utama</Badge>
            : <Badge variant="outline">Simpanan</Badge>;
    };

    const clearFilters = () => {
        setSelectedZone('');
        setSelectedSport('');
        setSearchTerm('');
        setCurrentPage(1);
    };

    return (
        <Card>
            <CardHeader>
                <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                    <div>
                        <CardTitle>Senarai Peserta</CardTitle>
                        <CardDescription>
                            Senarai lengkap peserta dari semua zon ({players.length} peserta)
                        </CardDescription>
                    </div>
                    <Button onClick={handleExport} variant="outline">
                        <Download className="w-4 h-4 mr-2" />
                        Export Excel
                    </Button>
                </div>
            </CardHeader>
            <CardContent className="space-y-4">
                {/* Filters */}
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <div className="space-y-2">
                        <label className="text-sm font-medium">Cari Peserta</label>
                        <div className="relative">
                            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                            <Input
                                placeholder="Nama peserta..."
                                value={searchTerm}
                                onChange={(e) => setSearchTerm(e.target.value)}
                                className="pl-10"
                            />
                        </div>
                    </div>
                    
                    <div className="space-y-2">
                        <label className="text-sm font-medium">Zon</label>
                        <Select value={selectedZone || undefined} onValueChange={setSelectedZone}>
                            <SelectTrigger className="w-full">
                                <SelectValue placeholder="Semua Zon" />
                            </SelectTrigger>
                            <SelectContent>
                                <SelectItem value="all">Semua Zon</SelectItem>
                                {filterOptions.zones.map((zone) => (
                                    <SelectItem key={zone.code} value={zone.code}>
                                        {zone.name}
                                    </SelectItem>
                                ))}
                            </SelectContent>
                        </Select>
                    </div>
                    
                    <div className="space-y-2">
                        <label className="text-sm font-medium">Sukan</label>
                        <Select value={selectedSport || undefined} onValueChange={setSelectedSport}>
                            <SelectTrigger className="w-full">
                                <SelectValue placeholder="Semua Sukan" />
                            </SelectTrigger>
                            <SelectContent>
                                <SelectItem value="all">Semua Sukan</SelectItem>
                                {filterOptions.sports.map((sport) => (
                                    <SelectItem key={sport.id} value={sport.id.toString()}>
                                        {sport.name}
                                    </SelectItem>
                                ))}
                            </SelectContent>
                        </Select>
                    </div>
                    
                    <div className="space-y-2">
                        <label className="text-sm font-medium">&nbsp;</label>
                        <Button variant="outline" onClick={clearFilters} className="w-full">
                            <Filter className="w-4 h-4 mr-2" />
                            Reset Filter
                        </Button>
                    </div>
                </div>

                {/* Table */}
                <div className="border rounded-lg">
                    <Table>
                        <TableHeader>
                            <TableRow>
                                <TableHead>Nama Peserta</TableHead>
                                <TableHead>Zon</TableHead>
                                <TableHead>Sukan</TableHead>
                                <TableHead>Jenis</TableHead>
                                <TableHead>Kategori Pangkat</TableHead>
                                <TableHead>Ketua Pasukan</TableHead>
                                <TableHead>Status</TableHead>
                                <TableHead>Tarikh Daftar</TableHead>
                                <TableHead className="text-center">Tindakan</TableHead>
                            </TableRow>
                        </TableHeader>
                        <TableBody>
                            {loading ? (
                                <TableRow>
                                    <TableCell colSpan={9} className="text-center py-8">
                                        <div className="flex items-center justify-center">
                                            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                                            <span className="ml-2">Memuat data...</span>
                                        </div>
                                    </TableCell>
                                </TableRow>
                            ) : players.length === 0 ? (
                                <TableRow>
                                    <TableCell colSpan={9} className="text-center py-8">
                                        <div className="text-gray-500">
                                            <p>Tiada peserta dijumpai</p>
                                            <p className="text-sm">Cuba ubah kriteria carian anda</p>
                                        </div>
                                    </TableCell>
                                </TableRow>
                            ) : (
                                players.map((player) => (
                                    <TableRow key={player.id}>
                                        <TableCell>
                                            <div>
                                                <p className="font-medium">{player.name}</p>
                                                <p className="text-sm text-gray-600">{player.phone}</p>
                                            </div>
                                        </TableCell>
                                        <TableCell>
                                            <Badge variant="outline">
                                                {player.zone_name || player.zone || '-'}
                                            </Badge>
                                        </TableCell>
                                        <TableCell>{player.sport || '-'}</TableCell>
                                        <TableCell>{getPlayerTypeBadge(player.player_type)}</TableCell>
                                        <TableCell>
                                            <Badge variant="secondary">
                                                {player.rank_category?.name || 'Tiada kategori'}
                                            </Badge>
                                        </TableCell>
                                        <TableCell>
                                            {player.team_leader_name ? (
                                                <div>
                                                    <p className="text-sm font-medium">{player.team_leader_name}</p>
                                                    <p className="text-xs text-gray-600">{player.team_leader_phone}</p>
                                                </div>
                                            ) : (
                                                <span className="text-gray-400">-</span>
                                            )}
                                        </TableCell>
                                        <TableCell>{getStatusBadge(player.status)}</TableCell>
                                        <TableCell>{player.created_at}</TableCell>
                                        <TableCell className="text-center">
                                            <Button
                                                variant="outline"
                                                size="sm"
                                                onClick={() => onViewPlayer(player)}
                                            >
                                                <Eye className="w-4 h-4" />
                                            </Button>
                                        </TableCell>
                                    </TableRow>
                                ))
                            )}
                        </TableBody>
                    </Table>
                </div>

                {/* Pagination */}
                {totalPages > 1 && (
                    <div className="flex items-center justify-between px-4 py-3 border-t">
                        <div className="text-sm text-gray-600">
                            Menunjukkan {((currentPage - 1) * perPage) + 1} hingga {Math.min(currentPage * perPage, totalPlayers)} daripada {totalPlayers} peserta
                        </div>
                        <div className="flex items-center space-x-2">
                            <Button
                                variant="outline"
                                size="sm"
                                onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                                disabled={currentPage === 1}
                            >
                                <ChevronLeft className="w-4 h-4" />
                                Sebelum
                            </Button>

                            <div className="flex items-center space-x-1">
                                {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                                    let pageNum;
                                    if (totalPages <= 5) {
                                        pageNum = i + 1;
                                    } else if (currentPage <= 3) {
                                        pageNum = i + 1;
                                    } else if (currentPage >= totalPages - 2) {
                                        pageNum = totalPages - 4 + i;
                                    } else {
                                        pageNum = currentPage - 2 + i;
                                    }

                                    return (
                                        <Button
                                            key={pageNum}
                                            variant={currentPage === pageNum ? "default" : "outline"}
                                            size="sm"
                                            onClick={() => setCurrentPage(pageNum)}
                                            className="w-8 h-8 p-0"
                                        >
                                            {pageNum}
                                        </Button>
                                    );
                                })}
                            </div>

                            <Button
                                variant="outline"
                                size="sm"
                                onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                                disabled={currentPage === totalPages}
                            >
                                Seterusnya
                                <ChevronRight className="w-4 h-4" />
                            </Button>
                        </div>
                    </div>
                )}
            </CardContent>
        </Card>
    );
}
