import { useState, useEffect } from 'react';
import publicApi from '@/lib/public-api';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Link } from 'react-router-dom';
import {
    Trophy,
    Calendar,
    Clock,
    MapPin,
    Users,
    Eye,
    ArrowRight,
    Zap
} from 'lucide-react';

interface LiveMatch {
    id: number;
    title: string;
    zone_a: string;
    zone_b: string;
    match_date: string;
    match_time: string;
    venue: string;
    court_field: string;
    status: string;
    sport: {
        id: number;
        name: string;
        logo_url?: string;
    };
    sports_schedule: {
        id: number;
        schedule_name: string;
    };
}

interface MatchStatistics {
    total_matches: number;
    completed_matches: number;
    upcoming_matches: number;
    ongoing_matches: number;
    total_schedules: number;
    active_sports: number;
}

export default function LiveMatchesWidget() {
    const [liveMatches, setLiveMatches] = useState<LiveMatch[]>([]);
    const [upcomingMatches, setUpcomingMatches] = useState<LiveMatch[]>([]);
    const [statistics, setStatistics] = useState<MatchStatistics | null>(null);
    const [isLoading, setIsLoading] = useState(true);

    useEffect(() => {
        fetchData();
        
        // Set up auto-refresh every 30 seconds for live data
        const interval = setInterval(fetchData, 30000);
        
        return () => clearInterval(interval);
    }, []);

    const fetchData = async () => {
        try {
            const [liveRes, upcomingRes, statsRes] = await Promise.all([
                publicApi.get('/public/jadual/live-matches'),
                publicApi.get('/public/jadual/upcoming-matches'),
                publicApi.get('/public/jadual/statistics')
            ]);

            setLiveMatches(liveRes.data.data || []);
            setUpcomingMatches(upcomingRes.data.data || []);
            setStatistics(statsRes.data);
        } catch (error) {
            console.error('Error fetching live data:', error);
        } finally {
            setIsLoading(false);
        }
    };

    const formatDateTime = (date: string, time: string) => {
        const matchDate = new Date(date);
        const today = new Date();
        const tomorrow = new Date(today);
        tomorrow.setDate(tomorrow.getDate() + 1);

        let dateStr = '';
        if (matchDate.toDateString() === today.toDateString()) {
            dateStr = 'Hari Ini';
        } else if (matchDate.toDateString() === tomorrow.toDateString()) {
            dateStr = 'Esok';
        } else {
            dateStr = matchDate.toLocaleDateString('ms-MY', { 
                day: 'numeric',
                month: 'short'
            });
        }

        return {
            date: dateStr,
            time: time
        };
    };

    if (isLoading) {
        return (
            <div className="space-y-6">
                <div className="animate-pulse">
                    <div className="h-32 bg-gray-200 rounded-lg"></div>
                </div>
            </div>
        );
    }

    return (
        <div className="space-y-6">
            {/* Statistics Overview */}
            {statistics && (
                <div className="grid gap-4 md:grid-cols-4">
                    <Card>
                        <CardContent className="p-4">
                            <div className="flex items-center justify-between">
                                <div>
                                    <p className="text-sm text-gray-600">Jumlah Perlawanan</p>
                                    <p className="text-2xl font-bold text-blue-600">{statistics.total_matches}</p>
                                </div>
                                <Trophy className="h-8 w-8 text-blue-600" />
                            </div>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardContent className="p-4">
                            <div className="flex items-center justify-between">
                                <div>
                                    <p className="text-sm text-gray-600">Sedang Berlangsung</p>
                                    <p className="text-2xl font-bold text-red-600">{statistics.ongoing_matches}</p>
                                </div>
                                <Zap className="h-8 w-8 text-red-600" />
                            </div>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardContent className="p-4">
                            <div className="flex items-center justify-between">
                                <div>
                                    <p className="text-sm text-gray-600">Akan Datang</p>
                                    <p className="text-2xl font-bold text-green-600">{statistics.upcoming_matches}</p>
                                </div>
                                <Calendar className="h-8 w-8 text-green-600" />
                            </div>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardContent className="p-4">
                            <div className="flex items-center justify-between">
                                <div>
                                    <p className="text-sm text-gray-600">Selesai</p>
                                    <p className="text-2xl font-bold text-gray-600">{statistics.completed_matches}</p>
                                </div>
                                <Trophy className="h-8 w-8 text-gray-600" />
                            </div>
                        </CardContent>
                    </Card>
                </div>
            )}

            {/* Live Matches */}
            {liveMatches.length > 0 && (
                <Card className="border-red-200 bg-red-50">
                    <CardHeader>
                        <CardTitle className="flex items-center gap-2 text-red-700">
                            <Zap className="h-5 w-5 animate-pulse" />
                            Perlawanan Sedang Berlangsung
                        </CardTitle>
                        <CardDescription>
                            {liveMatches.length} perlawanan sedang berlangsung sekarang
                        </CardDescription>
                    </CardHeader>
                    <CardContent>
                        <div className="space-y-4">
                            {liveMatches.slice(0, 3).map((match) => {
                                const dateTime = formatDateTime(match.match_date, match.match_time);
                                return (
                                    <div key={match.id} className="bg-white rounded-lg p-4 border border-red-200">
                                        <div className="flex items-center justify-between">
                                            <div className="flex-1">
                                                <div className="flex items-center gap-2 mb-2">
                                                    {match.sport.logo_url && (
                                                        <img 
                                                            src={match.sport.logo_url} 
                                                            alt={match.sport.name}
                                                            className="w-6 h-6 object-contain"
                                                        />
                                                    )}
                                                    <span className="font-medium text-sm">{match.sport.name}</span>
                                                    <Badge className="bg-red-100 text-red-800 animate-pulse">
                                                        LIVE
                                                    </Badge>
                                                </div>
                                                <h4 className="font-semibold">{match.title}</h4>
                                                <div className="flex items-center gap-4 text-sm text-gray-600 mt-1">
                                                    <span className="font-medium">{match.zone_a} vs {match.zone_b}</span>
                                                    <div className="flex items-center gap-1">
                                                        <MapPin className="h-3 w-3" />
                                                        {match.venue}
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                );
                            })}
                        </div>
                    </CardContent>
                </Card>
            )}

            {/* Upcoming Matches */}
            {upcomingMatches.length > 0 && (
                <Card>
                    <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                            <Calendar className="h-5 w-5" />
                            Perlawanan Akan Datang
                        </CardTitle>
                        <CardDescription>
                            Perlawanan yang akan berlangsung dalam 7 hari akan datang
                        </CardDescription>
                    </CardHeader>
                    <CardContent>
                        <div className="space-y-4">
                            {upcomingMatches.slice(0, 5).map((match) => {
                                const dateTime = formatDateTime(match.match_date, match.match_time);
                                return (
                                    <div key={match.id} className="border rounded-lg p-4 hover:bg-gray-50 transition-colors">
                                        <div className="flex items-center justify-between">
                                            <div className="flex-1">
                                                <div className="flex items-center gap-2 mb-2">
                                                    {match.sport.logo_url && (
                                                        <img 
                                                            src={match.sport.logo_url} 
                                                            alt={match.sport.name}
                                                            className="w-6 h-6 object-contain"
                                                        />
                                                    )}
                                                    <span className="font-medium text-sm">{match.sport.name}</span>
                                                </div>
                                                <h4 className="font-semibold">{match.title}</h4>
                                                <div className="flex items-center gap-4 text-sm text-gray-600 mt-1">
                                                    <span className="font-medium">{match.zone_a} vs {match.zone_b}</span>
                                                    <div className="flex items-center gap-1">
                                                        <Clock className="h-3 w-3" />
                                                        {dateTime.date} • {dateTime.time}
                                                    </div>
                                                    <div className="flex items-center gap-1">
                                                        <MapPin className="h-3 w-3" />
                                                        {match.venue}
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                );
                            })}
                        </div>
                        
                        {upcomingMatches.length > 5 && (
                            <div className="mt-4 text-center">
                                <Link to="/jadual-pertandingan">
                                    <Button variant="outline" className="flex items-center gap-2">
                                        Lihat Semua Perlawanan
                                        <ArrowRight className="h-4 w-4" />
                                    </Button>
                                </Link>
                            </div>
                        )}
                    </CardContent>
                </Card>
            )}

            {/* No matches message */}
            {liveMatches.length === 0 && upcomingMatches.length === 0 && (
                <Card>
                    <CardContent className="flex flex-col items-center justify-center py-12">
                        <Trophy className="h-12 w-12 text-gray-400 mb-4" />
                        <h3 className="text-lg font-medium text-gray-900 mb-2">
                            Tiada Perlawanan Aktif
                        </h3>
                        <p className="text-gray-600 text-center mb-4">
                            Tiada perlawanan sedang berlangsung atau akan datang pada masa ini.
                        </p>
                        <Link to="/jadual-pertandingan">
                            <Button variant="outline">
                                Lihat Jadual Lengkap
                            </Button>
                        </Link>
                    </CardContent>
                </Card>
            )}
        </div>
    );
}
