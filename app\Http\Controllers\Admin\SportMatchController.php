<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\SportMatch;
use App\Models\SportsSchedule;
use App\Models\Sport;
use App\Models\Zone;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;

class SportMatchController extends Controller
{
    /**
     * Get bracket matches for a specific sport
     */
    public function getBracketBySport(Request $request, $sportId): JsonResponse
    {
        try {
            $matches = SportMatch::with(['sport', 'sportsSchedule'])
                ->where('sport_id', $sportId)
                ->whereNotNull('bracket_round')
                ->orderBy('bracket_position')
                ->orderBy('match_date')
                ->orderBy('match_time')
                ->get();

            // Add sport name to each match for easier processing
            $matches = $matches->map(function ($match) {
                $match->sport_name = $match->sport->name ?? '';
                return $match;
            });

            return response()->json([
                'success' => true,
                'data' => $matches,
                'message' => 'Bracket matches retrieved successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve bracket matches: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request): JsonResponse
    {
        $query = SportMatch::with(['sport', 'sportsSchedule', 'creator'])
            ->orderedByDateTime();

        // Filter by sport if provided
        if ($request->filled('sport_id')) {
            $query->where('sport_id', $request->sport_id);
        }

        // Filter by sports schedule if provided
        if ($request->filled('sports_schedule_id')) {
            $query->where('sports_schedule_id', $request->sports_schedule_id);
        }

        // Filter by status if provided
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // Filter by category if provided
        if ($request->filled('category')) {
            $query->where('category', $request->category);
        }

        // Filter by match type if provided
        if ($request->filled('match_type')) {
            $query->where('match_type', $request->match_type);
        }

        // Filter by date range if provided
        if ($request->filled('start_date')) {
            $query->where('match_date', '>=', $request->start_date);
        }

        if ($request->filled('end_date')) {
            $query->where('match_date', '<=', $request->end_date);
        }

        // Search by title if provided
        if ($request->filled('search')) {
            $query->where('title', 'like', '%' . $request->search . '%');
        }

        // Filter by bracket matches if provided
        if ($request->filled('has_bracket') && $request->boolean('has_bracket')) {
            $query->whereNotNull('bracket_round');
        }

        $matches = $query->paginate($request->get('per_page', 10));

        // Transform the data to ensure zone_a and zone_b are strings
        $matches->getCollection()->transform(function ($match) {
            $match->zone_a = (string) $match->zone_a;
            $match->zone_b = (string) $match->zone_b;
            $match->winner_zone = (string) $match->winner_zone;
            $match->sport_name = $match->sport ? $match->sport->name : 'Unknown Sport';

            // Ensure detailed_scores is properly formatted
            if ($match->detailed_scores) {
                $match->detailed_scores = is_string($match->detailed_scores)
                    ? json_decode($match->detailed_scores, true)
                    : $match->detailed_scores;
            }

            return $match;
        });

        return response()->json($matches);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'sport_id' => 'required|exists:sports,id',
            'title' => 'required|string|max:255',
            'event_type' => 'required|in:head_to_head,group_event,knockout_group,category_based',
            'zone_a' => 'nullable|string|max:20',
            'zone_b' => 'nullable|string|max:20',
            'participating_zones' => 'nullable|array',
            'participating_zones.*' => 'string|max:20',
            'match_date' => 'required|date',
            'match_time' => 'required|string',
            'venue' => 'nullable|string|max:255',
            'status' => 'required|in:scheduled,ongoing,completed,cancelled',
        ]);

        // Validate zone requirements based on event type
        if ($validated['event_type'] === 'head_to_head' || $validated['event_type'] === 'category_based') {
            if (empty($validated['zone_a']) || empty($validated['zone_b'])) {
                return response()->json([
                    'message' => 'Zone A and Zone B are required for head-to-head matches',
                    'errors' => ['zones' => ['Both zones must be selected for head-to-head matches']]
                ], 422);
            }
        } elseif ($validated['event_type'] === 'group_event' || $validated['event_type'] === 'knockout_group') {
            if (empty($validated['participating_zones']) || count($validated['participating_zones']) < 2) {
                return response()->json([
                    'message' => 'At least 2 zones are required for group events',
                    'errors' => ['zones' => ['At least 2 zones must be selected for group events']]
                ], 422);
            }
        }

        // Format match_time properly - just store as time string
        if (isset($validated['match_time'])) {
            // If it's in format like "09:09 AM", convert to 24-hour format
            $time = $validated['match_time'];
            if (strpos($time, 'AM') !== false || strpos($time, 'PM') !== false) {
                $validated['match_time'] = date('H:i:s', strtotime($time));
            } else {
                // Ensure it has seconds
                if (substr_count($time, ':') === 1) {
                    $validated['match_time'] = $time . ':00';
                }
            }
        }

        // Set created_by properly - use user's actual ID, not username
        $user = auth()->user();
        $validated['created_by'] = $user->id; // Use the actual ID field

        $match = SportMatch::create($validated);
        $match->load(['sport']);

        return response()->json([
            'message' => 'Match created successfully',
            'data' => $match
        ], 201);
    }

    /**
     * Display the specified resource.
     */
    public function show(SportMatch $sportMatch): JsonResponse
    {
        $sportMatch->load(['sport', 'sportsSchedule', 'creator']);

        // Ensure zone fields are strings
        $sportMatch->zone_a = (string) $sportMatch->zone_a;
        $sportMatch->zone_b = (string) $sportMatch->zone_b;
        $sportMatch->winner_zone = (string) $sportMatch->winner_zone;
        $sportMatch->sport_name = $sportMatch->sport ? $sportMatch->sport->name : 'Unknown Sport';

        // Ensure detailed_scores is properly formatted
        if ($sportMatch->detailed_scores) {
            $sportMatch->detailed_scores = is_string($sportMatch->detailed_scores)
                ? json_decode($sportMatch->detailed_scores, true)
                : $sportMatch->detailed_scores;
        }

        return response()->json([
            'data' => $sportMatch
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, SportMatch $sportMatch): JsonResponse
    {
        $validated = $request->validate([
            'sport_id' => 'required|exists:sports,id',
            'sports_schedule_id' => 'nullable|exists:sports_schedules,id',
            'bil' => 'nullable|integer|min:1',
            'title' => 'required|string|max:255',
            'match_type' => 'nullable|string|max:100',
            'event_type' => 'required|in:head_to_head,group_event,knockout_group,category_based',
            'zone_a' => 'nullable|string|max:20',
            'zone_b' => 'nullable|string|max:20',
            'special_teams' => 'nullable|string|max:100',
            'participating_zones' => 'nullable|array',
            'participating_zones.*' => 'string|max:20',
            'match_date' => 'required|date',
            'match_time' => 'required|string',
            'end_time' => 'nullable|string',
            'venue' => 'nullable|string|max:255',
            'court_field' => 'nullable|string|max:10',
            'category' => 'nullable|string|max:100',
            'gender_type' => 'nullable|in:Lelaki,Perempuan,Mixed',
            'status' => 'required|in:scheduled,ongoing,completed,cancelled',
            'score_zone_a' => 'nullable|integer|min:0',
            'score_zone_b' => 'nullable|integer|min:0',
            'points_zone_a' => 'nullable|integer|min:0',
            'points_zone_b' => 'nullable|integer|min:0',
            'time_result_a' => 'nullable|string|max:20',
            'time_result_b' => 'nullable|string|max:20',
            'zone_rankings' => 'nullable|array',
            'detailed_scores' => 'nullable|array',
            'winner_zone' => 'nullable|string|max:20',
            'is_highlighted' => 'boolean',
            'highlight_color' => 'nullable|string|max:50',
            'bracket_round' => 'nullable|string|max:100',
            'bracket_position' => 'nullable|integer|min:1',
            'notes' => 'nullable|string',
        ]);

        // Validate zone requirements based on event type
        if ($validated['event_type'] === 'head_to_head' || $validated['event_type'] === 'category_based') {
            if (empty($validated['zone_a']) || empty($validated['zone_b'])) {
                return response()->json([
                    'message' => 'Zone A and Zone B are required for head-to-head matches',
                    'errors' => ['zones' => ['Both zones must be selected for head-to-head matches']]
                ], 422);
            }
        } elseif ($validated['event_type'] === 'group_event' || $validated['event_type'] === 'knockout_group') {
            if (empty($validated['participating_zones']) || count($validated['participating_zones']) < 2) {
                return response()->json([
                    'message' => 'At least 2 zones are required for group events',
                    'errors' => ['zones' => ['At least 2 zones must be selected for group events']]
                ], 422);
            }
        }

        // Format match_time properly
        if (isset($validated['match_time'])) {
            $time = $validated['match_time'];
            if (strpos($time, 'AM') !== false || strpos($time, 'PM') !== false) {
                $validated['match_time'] = date('H:i:s', strtotime($time));
            } else {
                if (substr_count($time, ':') === 1) {
                    $validated['match_time'] = $time . ':00';
                }
            }
        }

        // Format end_time properly
        if (isset($validated['end_time']) && !empty($validated['end_time'])) {
            $time = $validated['end_time'];
            if (strpos($time, 'AM') !== false || strpos($time, 'PM') !== false) {
                $validated['end_time'] = date('H:i:s', strtotime($time));
            } else {
                if (substr_count($time, ':') === 1) {
                    $validated['end_time'] = $time . ':00';
                }
            }
        }

        $sportMatch->update($validated);
        $sportMatch->load(['sport', 'sportsSchedule', 'creator', 'zoneA', 'zoneB', 'winnerZone']);

        return response()->json([
            'message' => 'Match updated successfully',
            'data' => $sportMatch
        ]);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(SportMatch $sportMatch): JsonResponse
    {
        $sportMatch->delete();

        return response()->json([
            'message' => 'Match deleted successfully'
        ]);
    }

    /**
     * Update match result (score and winner) with point calculation
     */
    public function updateResult(Request $request, SportMatch $sportMatch): JsonResponse
    {
        $validated = $request->validate([
            'score_zone_a' => 'required|integer|min:0',
            'score_zone_b' => 'required|integer|min:0',
            'points_zone_a' => 'nullable|integer|min:0',
            'points_zone_b' => 'nullable|integer|min:0',
            'winner_zone' => 'nullable|string',
            'notes' => 'nullable|string',
            'status' => 'required|string|in:scheduled,ongoing,completed,cancelled',
            'detailed_scores' => 'nullable|string', // For set-based sports JSON string
            'zone_rankings' => 'nullable|array', // For group events
            'zone_rankings.*.zone_code' => 'required_with:zone_rankings|string',
            'zone_rankings.*.final_score' => 'required_with:zone_rankings',
            'zone_rankings.*.ranking_position' => 'required_with:zone_rankings|integer|min:1',
            'zone_rankings.*.time_result' => 'nullable|string',
        ]);

        // Use submitted points if provided, otherwise calculate based on sport type
        if (isset($validated['points_zone_a']) && isset($validated['points_zone_b'])) {
            // Use the submitted points directly (for point-based sports like Ping Pong)
            $updateData = $validated;
        } else {
            // Calculate points based on sport type and result (for score-based sports)
            $pointsAwarded = $this->calculateMatchPoints($sportMatch, $validated);
            $updateData = array_merge($validated, [
                'points_zone_a' => $pointsAwarded['points_zone_a'] ?? 0,
                'points_zone_b' => $pointsAwarded['points_zone_b'] ?? 0,
            ]);
        }

        // Ensure detailed_scores is always included if provided
        if (isset($validated['detailed_scores']) && !empty($validated['detailed_scores'])) {
            \Log::info('Saving detailed_scores:', ['detailed_scores' => $validated['detailed_scores']]);
            // Parse JSON string to array for proper storage
            $detailedScores = is_string($validated['detailed_scores'])
                ? json_decode($validated['detailed_scores'], true)
                : $validated['detailed_scores'];
            $updateData['detailed_scores'] = $detailedScores;
        } else {
            \Log::info('No detailed_scores provided in request');
        }

        // Handle zone rankings for group events
        if (isset($validated['zone_rankings'])) {
            $updateData['zone_rankings'] = $validated['zone_rankings'];

            // Store individual zone results
            $this->storeZoneResults($sportMatch, $validated['zone_rankings']);
        }

        $sportMatch->update($updateData);
        $sportMatch->load(['sport', 'sportsSchedule', 'creator', 'zoneA', 'zoneB', 'winnerZone']);

        // Auto-generate next round if this is a tournament match
        if ($sportMatch->bracket_round && $sportMatch->status === 'completed' && $sportMatch->winner_zone) {
            $this->generateNextRoundMatch($sportMatch);
        }

        return response()->json([
            'message' => 'Keputusan perlawanan berjaya dikemaskini',
            'data' => $sportMatch
        ]);
    }

    /**
     * Calculate points awarded based on sport rules and match result
     */
    private function calculateMatchPoints(SportMatch $match, array $validated): array
    {
        $sportName = $match->sport->name ?? 'Bola Sepak';
        $scoreA = $validated['score_zone_a'];
        $scoreB = $validated['score_zone_b'];
        $winner = $validated['winner_zone'] ?? '';

        // Handle group events
        if (isset($validated['zone_rankings'])) {
            return ['points_zone_a' => 0, 'points_zone_b' => 0]; // Points stored in zone_rankings
        }

        // Head-to-head point calculation
        if ($winner === 'draw') {
            return ['points_zone_a' => 1, 'points_zone_b' => 1];
        }

        // Sport-specific point calculation
        switch ($sportName) {
            case 'Bola Sepak':
            case 'Bola Jaring':
                // Simple win/draw/loss system: 3/1/0
                if ($scoreA > $scoreB) {
                    return ['points_zone_a' => 3, 'points_zone_b' => 0];
                } else {
                    return ['points_zone_a' => 0, 'points_zone_b' => 3];
                }

            case 'Bola Tampar':
                // Based on sets won: 3/2/1/0
                if ($scoreA === 2 && $scoreB === 0) {
                    return ['points_zone_a' => 3, 'points_zone_b' => 0]; // 2-0 win
                } elseif ($scoreA === 2 && $scoreB === 1) {
                    return ['points_zone_a' => 2, 'points_zone_b' => 1]; // 2-1 win
                } elseif ($scoreA === 1 && $scoreB === 2) {
                    return ['points_zone_a' => 1, 'points_zone_b' => 2]; // 1-2 loss
                } elseif ($scoreA === 0 && $scoreB === 2) {
                    return ['points_zone_a' => 0, 'points_zone_b' => 3]; // 0-2 loss
                }
                break;

            case 'Badminton':
            case 'Ping Pong':
                // Based on categories/sets won: 3/2/1/0
                if ($scoreA === 3 && $scoreB === 0) {
                    return ['points_zone_a' => 3, 'points_zone_b' => 0]; // 3-0 win
                } elseif ($scoreA === 3 && ($scoreB === 1 || $scoreB === 2)) {
                    return ['points_zone_a' => 2, 'points_zone_b' => 1]; // 3-1 or 3-2 win
                } elseif (($scoreA === 1 || $scoreA === 2) && $scoreB === 3) {
                    return ['points_zone_a' => 1, 'points_zone_b' => 2]; // 1-3 or 2-3 loss
                } elseif ($scoreA === 0 && $scoreB === 3) {
                    return ['points_zone_a' => 0, 'points_zone_b' => 3]; // 0-3 loss
                }
                break;
        }

        // Default fallback
        return ['points_zone_a' => 0, 'points_zone_b' => 0];
    }

    /**
     * Store individual zone results for group events
     */
    private function storeZoneResults(SportMatch $match, array $zoneRankings): void
    {
        // Delete existing zone results for this match
        \App\Models\MatchZoneResult::where('match_id', $match->id)->delete();

        // Calculate points based on ranking position
        $pointsMap = [1 => 10, 2 => 8, 3 => 6, 4 => 4, 5 => 2, 6 => 1];

        foreach ($zoneRankings as $ranking) {
            \App\Models\MatchZoneResult::create([
                'match_id' => $match->id,
                'zone_code' => $ranking['zone_code'],
                'final_score' => $ranking['final_score'],
                'time_result' => $ranking['time_result'] ?? null,
                'ranking_position' => $ranking['ranking_position'],
                'points_earned' => $pointsMap[$ranking['ranking_position']] ?? 0,
            ]);
        }
    }

    /**
     * Get dropdown data for forms
     */
    public function getFormData(): JsonResponse
    {
        $sports = Sport::active()->ordered()->get(['id', 'name']);
        $zones = Zone::active()->ordered()->get(['code', 'name']);
        $sportsSchedules = SportsSchedule::active()->ordered()->get(['id', 'schedule_name', 'sport_id']);

        return response()->json([
            'sports' => $sports,
            'zones' => $zones,
            'sports_schedules' => $sportsSchedules,
            'categories' => [
                'Single Men',
                'Single Women',
                'Double Men A',
                'Double Men B',
                'Double Women',
                'Double Mix'
            ],
            'match_types' => [
                '1', '2', '3', '4', '5', '6',
                'Final',
                'Final Lelaki',
                'Final Perempuan',
                'Tempat ke 3 & 4'
            ],
            'bracket_rounds' => [
                'First Round',
                'Semi-Final',
                'Third Place',
                'Final'
            ],
            'court_fields' => ['A', 'B', 'C'],
            'highlight_colors' => [
                'kuning',
                'merah jambu',
                'hijau',
                'oren'
            ]
        ]);
    }

    /**
     * Generate next round match for tournament progression
     */
    private function generateNextRoundMatch(SportMatch $completedMatch)
    {
        // Logic to create next round match based on winner
        // This is a simplified version - you can expand based on your tournament structure

        $roundMapping = [
            'First Round' => 'Quarter Final',
            'Quarter Final' => 'Semi Final',
            'Semi Final' => 'Final',
        ];

        $nextRound = $roundMapping[$completedMatch->bracket_round] ?? null;

        if (!$nextRound || !$completedMatch->winner_zone || $completedMatch->winner_zone === 'draw') {
            return;
        }

        // Check if next round match already exists for this position
        $nextPosition = ceil($completedMatch->bracket_position / 2);

        $existingMatch = SportMatch::where('sport_id', $completedMatch->sport_id)
            ->where('bracket_round', $nextRound)
            ->where('bracket_position', $nextPosition)
            ->first();

        if (!$existingMatch) {
            // Create new match for next round
            SportMatch::create([
                'sport_id' => $completedMatch->sport_id,
                'sports_schedule_id' => $completedMatch->sports_schedule_id,
                'title' => $nextRound . ' - Match ' . $nextPosition,
                'bracket_round' => $nextRound,
                'bracket_position' => $nextPosition,
                'zone_a' => $completedMatch->winner_zone,
                'zone_b' => 'TBD', // To be determined from other match
                'match_date' => now()->addDays(1)->format('Y-m-d'),
                'match_time' => '10:00:00',
                'venue' => $completedMatch->venue,
                'status' => 'scheduled',
                'created_by' => auth()->id(),
            ]);
        } else {
            // Update existing match with winner
            if ($existingMatch->zone_a === 'TBD') {
                $existingMatch->update(['zone_a' => $completedMatch->winner_zone]);
            } elseif ($existingMatch->zone_b === 'TBD') {
                $existingMatch->update(['zone_b' => $completedMatch->winner_zone]);
            }
        }
    }

    /**
     * Get overall leaderboard/standings across all sports
     */
    public function getLeaderboard(Request $request): JsonResponse
    {
        try {
            // Get all completed matches with points
            $headToHeadPoints = SportMatch::where('status', 'completed')
                ->whereNotNull('winner_zone')
                ->with('sport')
                ->get()
                ->groupBy(function ($match) {
                    return $match->zone_a . '_' . $match->zone_b;
                })
                ->map(function ($matches) {
                    $zoneATotal = $matches->sum('points_zone_a');
                    $zoneBTotal = $matches->sum('points_zone_b');
                    $firstMatch = $matches->first();

                    return [
                        'zone_a' => $firstMatch->zone_a,
                        'zone_b' => $firstMatch->zone_b,
                        'points_a' => $zoneATotal,
                        'points_b' => $zoneBTotal,
                    ];
                });

            // Get group event points from MatchZoneResult
            $groupEventPoints = \App\Models\MatchZoneResult::with(['match.sport'])
                ->whereHas('match', function ($query) {
                    $query->where('status', 'completed');
                })
                ->get()
                ->groupBy('zone_code')
                ->map(function ($results, $zoneCode) {
                    return [
                        'zone_code' => $zoneCode,
                        'total_points' => $results->sum('points_earned'),
                        'matches_played' => $results->count(),
                        'breakdown' => $results->groupBy('match.sport.name')->map(function ($sportResults, $sportName) {
                            return [
                                'sport' => $sportName,
                                'points' => $sportResults->sum('points_earned'),
                                'matches' => $sportResults->count(),
                            ];
                        })->values(),
                    ];
                });

            // Combine all points and calculate overall standings
            $allZones = collect(['1', '2', '3', '4', '5', '6']); // Adjust based on actual zones
            $leaderboard = $allZones->map(function ($zoneCode) use ($headToHeadPoints, $groupEventPoints) {
                $headToHeadTotal = 0;
                $groupEventTotal = $groupEventPoints->get($zoneCode)['total_points'] ?? 0;

                // Calculate head-to-head points for this zone
                foreach ($headToHeadPoints as $match) {
                    if ($match['zone_a'] === $zoneCode) {
                        $headToHeadTotal += $match['points_a'];
                    } elseif ($match['zone_b'] === $zoneCode) {
                        $headToHeadTotal += $match['points_b'];
                    }
                }

                return [
                    'zone_code' => $zoneCode,
                    'zone_name' => "Zon {$zoneCode}",
                    'total_points' => $headToHeadTotal + $groupEventTotal,
                    'head_to_head_points' => $headToHeadTotal,
                    'group_event_points' => $groupEventTotal,
                    'matches_played' => $groupEventPoints->get($zoneCode)['matches_played'] ?? 0,
                ];
            })->sortByDesc('total_points')->values();

            return response()->json([
                'message' => 'Leaderboard retrieved successfully',
                'data' => $leaderboard,
                'summary' => [
                    'total_matches' => SportMatch::where('status', 'completed')->count(),
                    'total_points_awarded' => $leaderboard->sum('total_points'),
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to retrieve leaderboard',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
