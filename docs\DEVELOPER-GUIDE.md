# Developer Guide - Portal Sukan Intra KKD

## 📋 Table of Contents

1. [Development Environment](#development-environment)
2. [Code Structure](#code-structure)
3. [Best Practices](#best-practices)
4. [Frontend Development](#frontend-development)
5. [Backend Development](#backend-development)
6. [Database Management](#database-management)
7. [Testing](#testing)
8. [Performance Optimization](#performance-optimization)
9. [Security Guidelines](#security-guidelines)
10. [Contributing](#contributing)

---

## 🛠️ Development Environment

### Required Tools
- **PHP 8.2+** with extensions: BCMath, Ctype, Fileinfo, JSON, Mbstring, OpenSSL, PDO, Tokenizer, XML
- **Node.js 18+** with npm/yarn
- **MySQL 8.0+** or MariaDB 10.3+
- **Composer** for PHP dependency management
- **Git** for version control

### IDE Recommendations
- **VS Code** with extensions:
  - PHP Intelephense
  - Laravel Extension Pack
  - ES7+ React/Redux/React-Native snippets
  - Tailwind CSS IntelliSense
  - TypeScript Importer

### Development Setup
```bash
# Clone repository
git clone <repository-url>
cd intra-kor

# Install dependencies
composer install
npm install

# Environment setup
cp .env.example .env
php artisan key:generate

# Database setup
php artisan migrate
php artisan db:seed

# Start development servers
php artisan serve &
npm run dev
```

### Environment Variables
```env
# Development
APP_ENV=local
APP_DEBUG=true
APP_URL=http://localhost:8000

# Database
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=intra_kor
DB_USERNAME=root
DB_PASSWORD=

# Sanctum
SANCTUM_STATEFUL_DOMAINS=localhost:3000,127.0.0.1:3000
SESSION_DOMAIN=localhost
```

---

## 🏗️ Code Structure

### Backend Structure (Laravel)
```
app/
├── Http/
│   ├── Controllers/
│   │   ├── Api/           # API controllers
│   │   └── Controller.php # Base controller
│   ├── Middleware/        # Custom middleware
│   └── Requests/          # Form request validation
├── Models/                # Eloquent models
└── Providers/             # Service providers

database/
├── migrations/            # Database migrations
├── seeders/              # Database seeders
└── factories/            # Model factories

routes/
├── api.php               # API routes
├── web.php               # Web routes
└── console.php           # Console routes
```

### Frontend Structure (React)
```
resources/js/
├── components/
│   ├── admin/            # Admin-specific components
│   ├── public/           # Public page components
│   └── ui/               # Reusable UI components (Shadcn)
├── contexts/             # React contexts
├── hooks/                # Custom hooks
├── layouts/              # Layout components
├── lib/                  # Utility libraries
├── pages/                # Page components
├── router/               # Routing configuration
├── types/                # TypeScript type definitions
└── utils/                # Utility functions
```

### Database Schema Organization
```
Core Tables:
├── users                 # Authentication & user management
├── zones                 # Zone management
├── players               # Player registration
├── sports                # Sports categories
├── matches               # Match scheduling
└── tournament_results    # Results tracking

CMS Tables:
├── page_content          # Dynamic page content
├── site_settings         # System settings
├── news                  # News articles
└── gallery               # Media gallery

Relationship Tables:
├── player_sports         # Many-to-many: players & sports
└── personal_access_tokens # Sanctum tokens
```

---

## 💡 Best Practices

### General Principles
1. **Follow Laravel conventions** for naming and structure
2. **Use TypeScript** for all React components
3. **Implement proper error handling** at all levels
4. **Write descriptive commit messages** in English
5. **Use Bahasa Malaysia** for user-facing text
6. **Maintain consistent code formatting** with Prettier/Pint

### Code Quality Standards
- **PSR-12** coding standard for PHP
- **ESLint + Prettier** for JavaScript/TypeScript
- **Meaningful variable names** in English
- **Comprehensive comments** for complex logic
- **Unit tests** for critical functionality

### Git Workflow
```bash
# Feature development
git checkout -b feature/player-management
git add .
git commit -m "feat: add player registration form validation"
git push origin feature/player-management

# Create pull request for review
```

### Commit Message Convention
```
feat: add new feature
fix: bug fix
docs: documentation changes
style: formatting changes
refactor: code refactoring
test: adding tests
chore: maintenance tasks
```

---

## ⚛️ Frontend Development

### Component Guidelines

#### Component Structure
```typescript
// PlayerCard.tsx
import { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Player } from '@/types';

interface PlayerCardProps {
    player: Player;
    onEdit: (player: Player) => void;
    onDelete: (id: number) => void;
}

export default function PlayerCard({ player, onEdit, onDelete }: PlayerCardProps) {
    const [isLoading, setIsLoading] = useState(false);

    const handleDelete = async () => {
        setIsLoading(true);
        try {
            await onDelete(player.id);
        } finally {
            setIsLoading(false);
        }
    };

    return (
        <Card className="hover:shadow-lg transition-shadow">
            <CardHeader>
                <CardTitle>{player.name}</CardTitle>
            </CardHeader>
            <CardContent>
                <p className="text-sm text-muted-foreground">{player.zone}</p>
                <div className="flex gap-2 mt-4">
                    <Button onClick={() => onEdit(player)} variant="outline">
                        Edit
                    </Button>
                    <Button
                        onClick={handleDelete}
                        variant="destructive"
                        disabled={isLoading}
                    >
                        {isLoading ? 'Deleting...' : 'Delete'}
                    </Button>
                </div>
            </CardContent>
        </Card>
    );
}
```

#### State Management with TanStack Query
```typescript
// hooks/usePlayer.ts
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import api from '@/lib/axios';
import { Player, CreatePlayerData } from '@/types';

export function usePlayers(filters: PlayerFilters = {}) {
    return useQuery({
        queryKey: ['players', filters],
        queryFn: () => api.get('/admin/players', { params: filters }),
        staleTime: 5 * 60 * 1000, // 5 minutes
    });
}

export function useCreatePlayer() {
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: (data: CreatePlayerData) => api.post('/admin/players', data),
        onSuccess: () => {
            queryClient.invalidateQueries(['players']);
            toast.success('Peserta berjaya didaftarkan');
        },
        onError: (error) => {
            toast.error('Gagal mendaftarkan peserta');
        },
    });
}
```

#### Form Validation with Zod
```typescript
// schemas/playerSchema.ts
import { z } from 'zod';

export const playerSchema = z.object({
    name: z.string()
        .min(2, 'Nama mestilah sekurang-kurangnya 2 aksara')
        .max(100, 'Nama tidak boleh melebihi 100 aksara'),

    phone: z.string()
        .regex(/^(\+?6?01)[0-46-9]-*[0-9]{7,8}$/, 'Format no. telefon tidak sah'),

    email: z.string()
        .email('Format email tidak sah')
        .optional()
        .or(z.literal('')),

    zone: z.string()
        .min(1, 'Zon diperlukan'),

    sports: z.array(z.number())
        .min(1, 'Sekurang-kurangnya satu sukan mesti dipilih'),
});

export type PlayerFormData = z.infer<typeof playerSchema>;
```

#### Context Usage
```typescript
// contexts/auth-context.tsx
import { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import api from '@/lib/axios';

interface User {
    id: number;
    username: string;
    name: string;
    role: string;
    zone?: string;
}

interface AuthContextType {
    user: User | null;
    isLoading: boolean;
    login: (username: string, password: string) => Promise<void>;
    logout: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: ReactNode }) {
    const [user, setUser] = useState<User | null>(null);
    const [isLoading, setIsLoading] = useState(true);

    // Implementation...

    return (
        <AuthContext.Provider value={{ user, isLoading, login, logout }}>
            {children}
        </AuthContext.Provider>
    );
}

export function useAuth() {
    const context = useContext(AuthContext);
    if (context === undefined) {
        throw new Error('useAuth must be used within an AuthProvider');
    }
    return context;
}
```

### Styling Guidelines

#### Tailwind CSS Usage
```typescript
// Use consistent spacing and responsive design
<div className="space-y-6">
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        <Card className="hover:shadow-lg transition-shadow">
            <CardContent className="p-6">
                <h3 className="text-lg font-semibold mb-2">Title</h3>
                <p className="text-sm text-muted-foreground">Description</p>
            </CardContent>
        </Card>
    </div>
</div>

// Responsive breakpoints
const breakpoints = {
    sm: '640px',   // Small devices
    md: '768px',   // Medium devices
    lg: '1024px',  // Large devices
    xl: '1280px',  // Extra large devices
    '2xl': '1536px' // 2X large devices
};
```

#### Component Variants
```typescript
// Use class-variance-authority for component variants
import { cva, type VariantProps } from 'class-variance-authority';

const buttonVariants = cva(
    'inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors',
    {
        variants: {
            variant: {
                default: 'bg-primary text-primary-foreground hover:bg-primary/90',
                destructive: 'bg-destructive text-destructive-foreground hover:bg-destructive/90',
                outline: 'border border-input hover:bg-accent hover:text-accent-foreground',
            },
            size: {
                default: 'h-10 px-4 py-2',
                sm: 'h-9 rounded-md px-3',
                lg: 'h-11 rounded-md px-8',
            },
        },
        defaultVariants: {
            variant: 'default',
            size: 'default',
        },
    }
);
```

---

## 🔧 Backend Development

### Controller Guidelines

#### API Controller Structure
```php
<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Player;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Validation\ValidationException;

class PlayersController extends Controller
{
    /**
     * Display a listing of players
     */
    public function index(Request $request): JsonResponse
    {
        $query = Player::query();

        // Apply filters
        if ($request->has('search')) {
            $query->where('name', 'like', '%' . $request->search . '%');
        }

        if ($request->has('zone')) {
            $query->where('zone', $request->zone);
        }

        // Load relationships
        $players = $query->with(['sports:id,name', 'registeredBy:id,name'])
            ->paginate(10);

        return response()->json($players);
    }

    /**
     * Store a newly created player
     */
    public function store(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'name' => 'required|string|min:2|max:100',
            'phone' => 'required|string|regex:/^(\+?6?01)[0-46-9]-*[0-9]{7,8}$/',
            'email' => 'nullable|email',
            'zone' => 'required|string',
            'sports' => 'required|array|min:1',
            'sports.*' => 'exists:sports,id',
        ]);

        try {
            $player = Player::create([
                ...$validated,
                'registered_by' => auth()->id(),
            ]);

            $player->sports()->attach($validated['sports']);
            $player->load(['sports:id,name']);

            return response()->json([
                'message' => 'Peserta berjaya didaftarkan',
                'player' => $player,
            ], 201);

        } catch (\Exception $e) {
            \Log::error('Player creation failed', [
                'error' => $e->getMessage(),
                'data' => $validated,
            ]);

            return response()->json([
                'message' => 'Gagal mendaftarkan peserta',
            ], 500);
        }
    }
}
```

#### Model Guidelines
```php
<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

class Player extends Model
{
    protected $fillable = [
        'name',
        'phone',
        'email',
        'zone',
        'team_leader_name',
        'team_leader_phone',
        'status',
        'registered_by',
    ];

    protected function casts(): array
    {
        return [
            'is_active' => 'boolean',
        ];
    }

    /**
     * Relationships
     */
    public function registeredBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'registered_by');
    }

    public function sports(): BelongsToMany
    {
        return $this->belongsToMany(Sport::class, 'player_sports');
    }

    public function zoneModel(): BelongsTo
    {
        return $this->belongsTo(Zone::class, 'zone', 'code');
    }

    /**
     * Scopes
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    public function scopeByZone($query, string $zone)
    {
        return $query->where('zone', $zone);
    }

    /**
     * Accessors
     */
    public function getFullNameAttribute(): string
    {
        return $this->name;
    }
}
```

#### Request Validation
```php
<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StorePlayerRequest extends FormRequest
{
    public function authorize(): bool
    {
        return auth()->check();
    }

    public function rules(): array
    {
        return [
            'name' => 'required|string|min:2|max:100',
            'phone' => 'required|string|regex:/^(\+?6?01)[0-46-9]-*[0-9]{7,8}$/',
            'email' => 'nullable|email|max:255',
            'zone' => 'required|string|exists:zones,code',
            'sports' => 'required|array|min:1',
            'sports.*' => 'exists:sports,id',
            'team_leader_name' => 'required|string|max:100',
            'team_leader_phone' => 'required|string|regex:/^(\+?6?01)[0-46-9]-*[0-9]{7,8}$/',
        ];
    }

    public function messages(): array
    {
        return [
            'name.required' => 'Nama diperlukan',
            'name.min' => 'Nama mestilah sekurang-kurangnya 2 aksara',
            'phone.required' => 'No. telefon diperlukan',
            'phone.regex' => 'Format no. telefon tidak sah',
            'zone.required' => 'Zon diperlukan',
            'sports.required' => 'Sekurang-kurangnya satu sukan mesti dipilih',
        ];
    }
}
```

### Database Guidelines

#### Migration Best Practices
```php
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('players', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('phone', 20);
            $table->string('email')->nullable();
            $table->string('zone', 50);
            $table->string('team_leader_name');
            $table->string('team_leader_phone', 20);
            $table->enum('status', ['active', 'inactive'])->default('active');
            $table->foreignId('registered_by')->constrained('users');
            $table->timestamps();

            // Indexes for performance
            $table->index(['zone', 'status']);
            $table->index('status');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('players');
    }
};
```

#### Seeder Implementation
```php
<?php

namespace Database\Seeders;

use App\Models\Player;
use App\Models\Sport;
use App\Models\User;
use Illuminate\Database\Seeder;

class PlayerSeeder extends Seeder
{
    public function run(): void
    {
        $zones = ['Zon A', 'Zon B', 'Zon C'];
        $sports = Sport::all();
        $registrar = User::where('role', 'superadmin')->first();

        foreach ($zones as $zone) {
            for ($i = 1; $i <= 10; $i++) {
                $player = Player::create([
                    'name' => "Player {$zone} {$i}",
                    'phone' => '012345678' . $i,
                    'email' => "player{$zone}{$i}@example.com",
                    'zone' => $zone,
                    'team_leader_name' => "Leader {$zone}",
                    'team_leader_phone' => '0198765432',
                    'status' => 'active',
                    'registered_by' => $registrar->id,
                ]);

                // Attach random sports
                $randomSports = $sports->random(rand(1, 3));
                $player->sports()->attach($randomSports->pluck('id'));
            }
        }
    }
}
```

---

## 🗄️ Database Management

### Query Optimization

#### Efficient Queries
```php
// Good: Use eager loading to prevent N+1 queries
$players = Player::with(['sports:id,name', 'registeredBy:id,name'])
    ->where('status', 'active')
    ->paginate(10);

// Good: Use specific columns when possible
$players = Player::select(['id', 'name', 'zone', 'status'])
    ->where('zone', 'Zon A')
    ->get();

// Good: Use database-level filtering
$activePlayersCount = Player::where('status', 'active')->count();

// Avoid: Loading unnecessary relationships
$players = Player::with('sports')->get(); // If you don't need sports data

// Avoid: N+1 queries
foreach ($players as $player) {
    echo $player->sports->count(); // This will cause N+1 queries
}
```

#### Index Strategy
```sql
-- Primary indexes (automatically created)
PRIMARY KEY (id)

-- Foreign key indexes
INDEX idx_players_zone (zone)
INDEX idx_players_registered_by (registered_by)

-- Composite indexes for common queries
INDEX idx_players_zone_status (zone, status)
INDEX idx_matches_date_status (match_date, status)

-- Search indexes
INDEX idx_players_name (name)
FULLTEXT INDEX ft_players_name (name)
```

### Backup Strategy

#### Automated Backup Script
```bash
#!/bin/bash
# backup.sh

DB_NAME="intra_kor"
DB_USER="root"
DB_PASS="password"
BACKUP_DIR="/var/backups/mysql"
DATE=$(date +%Y%m%d_%H%M%S)

# Create backup directory
mkdir -p $BACKUP_DIR

# Database backup
mysqldump -u$DB_USER -p$DB_PASS $DB_NAME > $BACKUP_DIR/db_backup_$DATE.sql

# Compress backup
gzip $BACKUP_DIR/db_backup_$DATE.sql

# File backup
tar -czf $BACKUP_DIR/files_backup_$DATE.tar.gz storage/app/public/

# Clean old backups (keep last 7 days)
find $BACKUP_DIR -name "*.gz" -mtime +7 -delete

echo "Backup completed: $DATE"
```

#### Restore Process
```bash
# Restore database
gunzip db_backup_20250714_120000.sql.gz
mysql -u root -p intra_kor < db_backup_20250714_120000.sql

# Restore files
tar -xzf files_backup_20250714_120000.tar.gz -C /path/to/laravel/
```

---

**Portal Sukan Intra Kor Kesihatan DiRaja 2025 - Developer Guide**
Last Updated: July 2025
Version: 1.0.0