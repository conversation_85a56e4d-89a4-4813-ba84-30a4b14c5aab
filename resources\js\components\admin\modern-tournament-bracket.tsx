import React, { useState, useEffect } from 'react';
import { Bracket, RoundProps, Seed, SeedItem, SeedTeam, RenderSeedProps } from '@sportsgram/brackets';
import api from '@/lib/axios';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { RefreshCw, Trophy, Zap, Loader2 } from 'lucide-react';
import toast from 'react-hot-toast';
import { ZoneLogo } from '@/components/ui/logo-components';

// Helper function to format date to Malaysian format
const formatMalaysianDate = (dateStr: string): string => {
    try {
        const date = new Date(dateStr);
        return date.toLocaleDateString('ms-MY', {
            day: '2-digit',
            month: '2-digit',
            year: 'numeric'
        });
    } catch {
        return dateStr;
    }
};

// Helper function to format time to Malaysian format
const formatMalaysianTime = (timeStr: string): string => {
    try {
        // Handle both HH:mm and HH:mm:ss formats
        const timeParts = timeStr.split(':');
        const hours = parseInt(timeParts[0]);
        const minutes = timeParts[1];

        return `${hours.toString().padStart(2, '0')}:${minutes}`;
    } catch {
        return timeStr;
    }
};

interface DatabaseMatch {
    id: number;
    title: string;
    match_type: string;
    zone_a: string;
    zone_b: string;
    match_date: string;
    match_time: string;
    venue: string;
    status: 'scheduled' | 'ongoing' | 'completed' | 'cancelled';
    bracket_round: string;
    bracket_position: number;
    is_highlighted?: boolean;
    score_zone_a?: number;
    score_zone_b?: number;
}

interface ModernTournamentBracketProps {
    onMatchSelect?: (match: any) => void;
}

const ModernTournamentBracket: React.FC<ModernTournamentBracketProps> = ({ onMatchSelect }) => {
    const [selectedSport, setSelectedSport] = useState<string>('');
    const [sports, setSports] = useState<any[]>([]);
    const [bracketData, setBracketData] = useState<RoundProps[]>([]);
    const [isLoading, setIsLoading] = useState(false);

    // Helper function to get proper team display name
    const getTeamDisplayName = (zoneCode: string): string => {
        if (!zoneCode) return 'Menunggu Pemenang';

        // Handle placeholder codes
        const placeholders: Record<string, string> = {
            'W1': 'Pemenang 1',
            'W2': 'Pemenang 2',
            'W3': 'Pemenang 3',
            'W4': 'Pemenang 4',
            'L1': 'Kalah 1',
            'L2': 'Kalah 2',
            'L3': 'Kalah 3',
            'L4': 'Kalah 4',
            'Y': 'Peserta Y',
            'Z': 'Peserta Z',
            'TBD': 'Menunggu'
        };

        if (placeholders[zoneCode]) {
            return placeholders[zoneCode];
        }

        // Handle regular zone codes
        if (zoneCode === 'BAKAT') return 'Bakat KKD';
        if (zoneCode.match(/^\d+$/)) return `Zon ${zoneCode}`;
        if (zoneCode !== 'Zon X') return `Zon ${zoneCode}`;

        return 'Menunggu Pemenang';
    };

    // Fetch sports list
    useEffect(() => {
        const fetchSports = async () => {
            try {
                // Debug: Check if we have auth token
                const token = localStorage.getItem('auth_token');
                console.log('Auth token exists:', !!token);
                console.log('Token preview:', token ? token.substring(0, 20) + '...' : 'No token');

                const response = await api.get('/admin/sports');
                console.log('Sports API response:', response.data);
                // API returns direct array, not wrapped in success/data
                setSports(response.data || []);
                toast.success(`${response.data.length} sukan dimuat berjaya`);
            } catch (error: any) {
                console.error('Error fetching sports:', error);
                console.error('Error response:', error.response?.data);
                console.error('Error status:', error.response?.status);
                toast.error(`Gagal memuat senarai sukan: ${error.response?.status || 'Unknown error'}`);
            }
        };

        fetchSports();
    }, []);

    // Convert database matches to @sportsgram/brackets format
    const convertToSportsgramFormat = (dbMatches: DatabaseMatch[]): RoundProps[] => {
        // Group matches by bracket round
        const roundGroups: Record<string, DatabaseMatch[]> = {};
        
        dbMatches.forEach(match => {
            const round = match.bracket_round || 'Unknown';
            if (!roundGroups[round]) {
                roundGroups[round] = [];
            }
            roundGroups[round].push(match);
        });

        // Convert to RoundProps format
        const rounds: RoundProps[] = Object.entries(roundGroups).map(([roundName, matches]) => ({
            title: roundName,
            seeds: matches.map(match => {
                console.log(`Converting match: ${match.title}`);
                console.log(`Zone A: ${match.zone_a} -> ${getTeamDisplayName(match.zone_a)}`);
                console.log(`Zone B: ${match.zone_b} -> ${getTeamDisplayName(match.zone_b)}`);

                return {
                    id: match.id,
                    date: `${formatMalaysianDate(match.match_date)} ${formatMalaysianTime(match.match_time)}`,
                    teams: [
                        {
                            name: getTeamDisplayName(match.zone_a),
                            score: match.score_zone_a
                        },
                        {
                            name: getTeamDisplayName(match.zone_b),
                            score: match.score_zone_b
                        }
                    ],
                    // Store original match data for callbacks
                    originalMatch: match
                };
            })
        }));

        return rounds;
    };

    // Fetch bracket matches for selected sport
    const fetchBracketMatches = async () => {
        if (!selectedSport) {
            toast.error('Sila pilih sukan terlebih dahulu');
            return;
        }

        setIsLoading(true);
        try {
            const timestamp = new Date().getTime();
            const response = await api.get(`/admin/matches/sport/${selectedSport}/bracket?t=${timestamp}`);
            console.log('Bracket API Response:', response.data);

            if (response.data.success) {
                const dbMatches: DatabaseMatch[] = response.data.data || [];
                console.log('DB Matches:', dbMatches);

                // Log each match details
                dbMatches.forEach(match => {
                    console.log(`Match: ${match.title} | ${match.zone_a} vs ${match.zone_b} | Round: ${match.bracket_round}`);
                });

                const rounds = convertToSportsgramFormat(dbMatches);
                console.log('Converted Rounds:', rounds);
                setBracketData(rounds);

                if (rounds.length === 0) {
                    toast.info('Tiada data bracket untuk sukan ini');
                } else {
                    toast.success(`${rounds.length} rounds bracket dimuat berjaya`);
                }
            } else {
                throw new Error(response.data.message || 'Failed to fetch bracket data');
            }
        } catch (error: any) {
            console.error('Error fetching bracket matches:', error);
            const errorMessage = error.response?.data?.message || error.message || 'Gagal memuat data bracket';
            toast.error(errorMessage);
            setBracketData([]);
        } finally {
            setIsLoading(false);
        }
    };

    // Custom Seed Component with Portal Sukan styling
    const CustomSeed = ({ seed, breakpoint, roundIndex, seedIndex }: RenderSeedProps) => {
        const match = (seed as any).originalMatch as DatabaseMatch;
        const team1 = seed.teams[0];
        const team2 = seed.teams[1];
        
        const team1Won = team1.score !== undefined && team2.score !== undefined && team1.score > team2.score;
        const team2Won = team1.score !== undefined && team2.score !== undefined && team2.score > team1.score;
        
        const handleSeedClick = () => {
            if (onMatchSelect && match) {
                onMatchSelect(match);
            }
        };

        return (
            <Seed 
                mobileBreakpoint={breakpoint} 
                style={{ fontSize: 14, cursor: 'pointer' }}
                onClick={handleSeedClick}
            >
                <SeedItem>
                    <div className="bg-white border-2 border-gray-200 rounded-lg p-4 hover:border-red-400 transition-colors min-w-[200px]">
                        {/* Match Header */}
                        <div className="text-center mb-3 border-b pb-2">
                            <div className="text-xs font-bold text-gray-700">
                                {match?.title || 'Match'}
                            </div>
                            <div className="text-xs text-gray-500">
                                {formatMalaysianDate(match?.match_date || '')} {formatMalaysianTime(match?.match_time || '')}
                            </div>
                        </div>

                        {/* Teams - Vertical Layout with Logos Only */}
                        <div className="flex flex-col items-center space-y-3">
                            {/* Team 1 */}
                            <div className="flex items-center gap-4">
                                <div className="relative">
                                    {team1.name.includes('Menunggu') ? (
                                        <div className="w-24 h-24 bg-gray-100 border-2 border-dashed border-gray-300 rounded-full flex items-center justify-center">
                                            <span className="text-xs text-gray-500 text-center">Menunggu<br/>Pemenang</span>
                                        </div>
                                    ) : (
                                        <ZoneLogo
                                            zoneCode={team1.name.replace('Zon ', '')}
                                            className="w-24 h-24"
                                        />
                                    )}
                                    {/* WIN Badge */}
                                    {team1Won && (
                                        <div className="absolute -top-2 -right-2 bg-blue-500 text-white text-xs font-bold px-2 py-1 rounded-md shadow-lg border-2 border-white">
                                            WIN
                                        </div>
                                    )}
                                </div>
                                <div className={`text-xl font-bold px-3 py-2 rounded ${team1Won ? 'bg-yellow-500 text-white' : 'bg-gray-200 text-gray-600'}`}>
                                    {team1.score ?? '0'}
                                </div>
                            </div>

                            {/* VS */}
                            <div className="text-center my-2">
                                <span className="text-sm text-red-600 font-bold bg-red-100 px-3 py-2 rounded-full">VS</span>
                            </div>

                            {/* Team 2 */}
                            <div className="flex items-center gap-4">
                                <div className="relative">
                                    {team2.name.includes('Menunggu') ? (
                                        <div className="w-24 h-24 bg-gray-100 border-2 border-dashed border-gray-300 rounded-full flex items-center justify-center">
                                            <span className="text-xs text-gray-500 text-center">Menunggu<br/>Pemenang</span>
                                        </div>
                                    ) : (
                                        <ZoneLogo
                                            zoneCode={team2.name.replace('Zon ', '')}
                                            className="w-24 h-24"
                                        />
                                    )}
                                    {/* WIN Badge */}
                                    {team2Won && (
                                        <div className="absolute -top-2 -right-2 bg-blue-500 text-white text-xs font-bold px-2 py-1 rounded-md shadow-lg border-2 border-white">
                                            WIN
                                        </div>
                                    )}
                                </div>
                                <div className={`text-xl font-bold px-3 py-2 rounded ${team2Won ? 'bg-yellow-500 text-white' : 'bg-gray-200 text-gray-600'}`}>
                                    {team2.score ?? '0'}
                                </div>
                            </div>
                        </div>

                        {/* Match Status */}
                        <div className="mt-2 text-center">
                            <span className={`text-xs px-2 py-1 rounded-full ${
                                match?.status === 'completed' ? 'bg-green-100 text-green-800' :
                                match?.status === 'ongoing' ? 'bg-blue-100 text-blue-800' :
                                'bg-gray-100 text-gray-800'
                            }`}>
                                {match?.status === 'completed' ? 'Selesai' :
                                 match?.status === 'ongoing' ? 'Berlangsung' : 'Dijadualkan'}
                            </span>
                        </div>
                    </div>
                </SeedItem>
            </Seed>
        );
    };

    return (
        <div className="space-y-6">
            {/* Header with integrated controls */}
            <Card>
                <CardHeader>
                    <div className="flex items-center justify-between">
                        <div>
                            <CardTitle className="flex items-center gap-2">
                                <Trophy className="h-6 w-6 text-yellow-600" />
                                Tournament Bracket Manager
                            </CardTitle>
                            <CardDescription>
                                Urus dan papar bracket tournament untuk setiap sukan
                            </CardDescription>
                        </div>
                        <div className="flex gap-4 items-end">
                            <div className="min-w-[200px]">
                                <label className="block text-sm font-medium mb-2">Pilih Sukan</label>
                                <Select value={selectedSport} onValueChange={setSelectedSport}>
                                    <SelectTrigger>
                                        <SelectValue placeholder="Pilih sukan..." />
                                    </SelectTrigger>
                                    <SelectContent>
                                        {sports.map((sport) => (
                                            <SelectItem key={sport.id} value={sport.id.toString()}>
                                                {sport.name}
                                            </SelectItem>
                                        ))}
                                    </SelectContent>
                                </Select>
                            </div>
                            <Button
                                onClick={fetchBracketMatches}
                                disabled={!selectedSport || isLoading}
                                className="bg-red-600 hover:bg-red-700"
                            >
                                {isLoading ? (
                                    <Loader2 className="h-4 w-4 animate-spin mr-2" />
                                ) : (
                                    <RefreshCw className="h-4 w-4 mr-2" />
                                )}
                                Load Bracket (v2)
                            </Button>
                        </div>
                    </div>
                </CardHeader>
            </Card>

            {/* Bracket Display */}
            <div className="w-full">
                {isLoading ? (
                    <Card>
                        <CardContent className="flex items-center justify-center py-12">
                            <div className="text-center">
                                <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-red-600" />
                                <p className="text-gray-600">Memuat bracket...</p>
                            </div>
                        </CardContent>
                    </Card>
                ) : bracketData.length > 0 ? (
                    <Card>
                        <CardContent className="p-6">
                            <div className="bg-white rounded-lg">
                                <Bracket
                                    rounds={bracketData}
                                    renderSeedComponent={CustomSeed}
                                    mobileBreakpoint={768}
                                    roundClassName="tournament-round"
                                    bracketClassName="tournament-bracket"
                                />
                            </div>
                        </CardContent>
                    </Card>
                ) : selectedSport ? (
                    <Card>
                        <CardContent className="text-center py-12">
                            <Trophy className="h-12 w-12 mx-auto mb-4 text-gray-400" />
                            <p className="text-gray-600 mb-2">Tiada bracket dijumpai untuk sukan ini</p>
                            <p className="text-sm text-gray-500">Pilih sukan lain atau jana bracket baru</p>
                        </CardContent>
                    </Card>
                ) : (
                    <Card>
                        <CardContent className="text-center py-12">
                            <Zap className="h-12 w-12 mx-auto mb-4 text-gray-400" />
                            <p className="text-gray-600">Pilih sukan untuk melihat tournament bracket</p>
                        </CardContent>
                    </Card>
                )}
            </div>
        </div>
    );
};

export default ModernTournamentBracket;
