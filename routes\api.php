<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

// Authentication routes
Route::post('/login', [App\Http\Controllers\Api\AuthController::class, 'login']);

// Protected API routes (require Sanctum authentication)
Route::middleware('auth:sanctum')->group(function () {
    // Logout (requires authentication)
    Route::post('/logout', [App\Http\Controllers\Api\AuthController::class, 'logout']);
    // User info
    Route::get('/user', [App\Http\Controllers\Api\AuthController::class, 'user']);
    
    // Password change
    Route::put('/password/change', [App\Http\Controllers\Api\AuthController::class, 'changePassword']);
    
    // Dashboard
    Route::get('/dashboard/stats', [App\Http\Controllers\Api\DashboardController::class, 'stats']);
    
    // Admin Routes
    Route::prefix('admin')->group(function () {
        // Players Management
        Route::get('players/check-zone-leader', [App\Http\Controllers\Api\PlayersController::class, 'checkZoneLeader']);
        Route::get('players/all', [App\Http\Controllers\Api\PlayersController::class, 'getAllPlayers']);
        Route::get('players/filter-options', [App\Http\Controllers\Api\PlayersController::class, 'getFilterOptions']);
        Route::apiResource('players', App\Http\Controllers\Api\PlayersController::class);
        Route::get('players/stats', [App\Http\Controllers\Api\PlayersController::class, 'getStats']);
        Route::post('players/rank-based', [App\Http\Controllers\Api\PlayersController::class, 'storeRankBased']);

        // Zones Management
        Route::apiResource('zones', App\Http\Controllers\Api\ZonesController::class);
        Route::patch('zones/{code}/logo', [App\Http\Controllers\Api\ZonesController::class, 'updateLogo']);
        Route::get('zones/{code}/users', [App\Http\Controllers\Api\ZonesController::class, 'getZoneUsers']);

        // Users Management
        Route::get('users', [App\Http\Controllers\Api\UsersController::class, 'index']);

        // Sports Management
        Route::apiResource('sports', App\Http\Controllers\Api\SportsController::class);
        Route::post('sports/sync', [App\Http\Controllers\Api\SportsController::class, 'sync']);

        // Rank Categories Management
        Route::apiResource('rank-categories', App\Http\Controllers\Api\RankCategoriesController::class)->only(['index', 'show']);

        // Sport Teams Management
        Route::get('sport-teams', [App\Http\Controllers\Api\SportTeamsController::class, 'index']);
        Route::get('sport-teams/{sportId}/details', [App\Http\Controllers\Api\SportTeamsController::class, 'show']);
        Route::post('sport-teams', [App\Http\Controllers\Api\SportTeamsController::class, 'store']);
        Route::put('sport-teams/{id}', [App\Http\Controllers\Api\SportTeamsController::class, 'update']);
        Route::delete('sport-teams/{id}', [App\Http\Controllers\Api\SportTeamsController::class, 'destroy']);

        // Rank Category Teams Management
        Route::post('rank-category-teams', [App\Http\Controllers\Api\RankCategoryTeamsController::class, 'store']);
        Route::put('rank-category-teams/{id}', [App\Http\Controllers\Api\RankCategoryTeamsController::class, 'update']);
        Route::delete('rank-category-teams/{id}', [App\Http\Controllers\Api\RankCategoryTeamsController::class, 'destroy']);
        Route::get('rank-category-teams/{sportId}/{rankCategoryId}', [App\Http\Controllers\Api\RankCategoryTeamsController::class, 'show']);

        // Jadual Pertandingan Management
        Route::apiResource('sports-schedules', App\Http\Controllers\Admin\SportsScheduleController::class);
        Route::get('sports-schedules/form-data/sports', [App\Http\Controllers\Admin\SportsScheduleController::class, 'getSports']);

        Route::apiResource('sport-matches', App\Http\Controllers\Admin\SportMatchController::class);
        Route::get('sport-matches/form-data/all', [App\Http\Controllers\Admin\SportMatchController::class, 'getFormData']);
        Route::put('sport-matches/{sportMatch}/result', [App\Http\Controllers\Admin\SportMatchController::class, 'updateResult']);
        Route::get('sport-matches/leaderboard', [App\Http\Controllers\Admin\SportMatchController::class, 'getLeaderboard']);

        // Tournament Bracket Routes
        Route::get('matches/sport/{sportId}/bracket', [App\Http\Controllers\Admin\SportMatchController::class, 'getBracketBySport']);

        Route::apiResource('tournament-brackets', App\Http\Controllers\Admin\TournamentBracketController::class);
        Route::get('tournament-brackets/form-data/sports', [App\Http\Controllers\Admin\TournamentBracketController::class, 'getSports']);
        Route::post('tournament-brackets/generate-next-round', [App\Http\Controllers\Admin\TournamentBracketController::class, 'generateNextRound']);
    });
    
    // Matches Management
    Route::apiResource('matches', App\Http\Controllers\Api\MatchesController::class);
    Route::get('matches/stats', [App\Http\Controllers\Api\MatchesController::class, 'getStats']);
    
    // Reports & Statistics
    Route::get('reports', [App\Http\Controllers\Api\ReportsController::class, 'index']);
    Route::get('reports/export', [App\Http\Controllers\Api\ReportsController::class, 'export']);
    
    // System Settings
    Route::get('settings', [App\Http\Controllers\Api\SettingsController::class, 'index']);
    Route::post('settings/update', [App\Http\Controllers\Api\SettingsController::class, 'updateSetting']);
    Route::post('settings/upload-logo', [App\Http\Controllers\Api\SettingsController::class, 'uploadLogo']);
    Route::post('settings/upload-favicon', [App\Http\Controllers\Api\SettingsController::class, 'uploadFavicon']);

    // Admin Settings
    Route::prefix('admin')->group(function () {
        Route::post('settings/general', [App\Http\Controllers\Api\SettingsController::class, 'updateGeneralSettings']);
        Route::post('settings/notification', [App\Http\Controllers\Api\SettingsController::class, 'updateNotificationSettings']);
    });
    
    // CMS Routes
    Route::prefix('cms')->group(function () {
        // Page Content Management
        Route::apiResource('page-content', App\Http\Controllers\Api\PageContentController::class);
        Route::put('page-content/{page}/update-page', [App\Http\Controllers\Api\PageContentController::class, 'updatePageContent']);

        // Image Upload and Processing
        Route::post('upload-image', [App\Http\Controllers\Api\ImageUploadController::class, 'uploadCmsImage']);
        Route::post('process-image-url', [App\Http\Controllers\Api\ImageUploadController::class, 'processImageUrl']);
        Route::post('get-image-info', [App\Http\Controllers\Api\ImageUploadController::class, 'getImageInfo']);
        Route::delete('delete-image', [App\Http\Controllers\Api\ImageUploadController::class, 'deleteImage']);
        Route::get('test-system', [App\Http\Controllers\Api\ImageUploadController::class, 'testSystem']);

        // Gallery Management (authenticated users can upload)
        Route::post('gallery', [App\Http\Controllers\Api\GalleryController::class, 'store']);
        Route::post('gallery/multiple', [App\Http\Controllers\Api\GalleryController::class, 'storeMultiple']);
        Route::put('gallery/{id}', [App\Http\Controllers\Api\GalleryController::class, 'update']);
        Route::delete('gallery/{id}', [App\Http\Controllers\Api\GalleryController::class, 'destroy']);

        // Rules Management - TODO: Create controllers
        // Route::get('rules', [App\Http\Controllers\Api\RulesController::class, 'index']);
        // Route::apiResource('rules/general', App\Http\Controllers\Api\GeneralRulesController::class);
        // Route::apiResource('rules/sports', App\Http\Controllers\Api\SportsRulesController::class);
        // Route::apiResource('rules/penalties', App\Http\Controllers\Api\PenaltiesController::class);
    });
});


// Public API routes (no authentication required)
Route::prefix('public')->group(function () {
    // Get page content
    Route::get('content/{page}/{section?}/{key}', [App\Http\Controllers\Api\PageContentController::class, 'getContent']);
    Route::get('content/{page}', [App\Http\Controllers\Api\PageContentController::class, 'getPageContent']);

    // Get public matches and results
    Route::get('matches', [App\Http\Controllers\Api\MatchesController::class, 'index']);
    Route::get('matches/{id}', [App\Http\Controllers\Api\MatchesController::class, 'show']);

    // Get public news and gallery
    Route::get('news', [App\Http\Controllers\Api\NewsController::class, 'index']);
    Route::get('news/featured', [App\Http\Controllers\Api\NewsController::class, 'featured']);
    Route::get('news/{id}', [App\Http\Controllers\Api\NewsController::class, 'show']);
    Route::post('news/{id}/view', [App\Http\Controllers\Api\NewsController::class, 'incrementView']);

    Route::get('gallery', [App\Http\Controllers\Api\GalleryController::class, 'index']);
    Route::get('gallery/featured', [App\Http\Controllers\Api\GalleryController::class, 'featured']);
    Route::get('gallery/{id}', [App\Http\Controllers\Api\GalleryController::class, 'show']);
    Route::post('gallery/{id}/view', [App\Http\Controllers\Api\GalleryController::class, 'incrementView']);

    // Get rules (public access)
    Route::get('rules/general', function () {
        return response()->json([
            ['id' => 1, 'title' => 'Peraturan Am 1', 'description' => 'Penerangan peraturan am...'],
            ['id' => 2, 'title' => 'Peraturan Am 2', 'description' => 'Penerangan peraturan am...'],
        ]);
    });
    Route::get('rules/sports', function () {
        return response()->json([
            ['id' => 1, 'sport' => 'Badminton', 'rules' => 'Peraturan badminton...'],
            ['id' => 2, 'sport' => 'Football', 'rules' => 'Peraturan football...'],
        ]);
    });
    Route::get('penalties', function () {
        return response()->json([
            ['id' => 1, 'violation' => 'Lewat hadir', 'penalty' => 'Amaran pertama'],
            ['id' => 2, 'violation' => 'Tidak hadir', 'penalty' => 'Diskualifikasi'],
        ]);
    });

    // Get public settings
    Route::get('settings', [App\Http\Controllers\Api\SettingsController::class, 'getPublicSettings']);

    // Get homepage content
    Route::get('homepage-content', [App\Http\Controllers\Api\PageContentController::class, 'getHomepageContent']);

    // Public Jadual Pertandingan routes
    Route::prefix('jadual')->group(function () {
        Route::get('sports-schedules', [App\Http\Controllers\Api\PublicJadualController::class, 'getSportsSchedules']);
        Route::get('matches', [App\Http\Controllers\Api\PublicJadualController::class, 'getMatches']);
        Route::get('matches/schedule/{scheduleId}', [App\Http\Controllers\Api\PublicJadualController::class, 'getMatchesBySchedule']);
        Route::get('tournament-brackets', [App\Http\Controllers\Api\PublicJadualController::class, 'getTournamentBrackets']);
        Route::get('live-matches', [App\Http\Controllers\Api\PublicJadualController::class, 'getLiveMatches']);
        Route::get('upcoming-matches', [App\Http\Controllers\Api\PublicJadualController::class, 'getUpcomingMatches']);
        Route::get('tournament-results', [App\Http\Controllers\Api\PublicJadualController::class, 'getTournamentResults']);
        Route::get('overall-standings', [App\Http\Controllers\Api\PublicJadualController::class, 'getOverallStandings']);
        Route::get('statistics', [App\Http\Controllers\Api\PublicJadualController::class, 'getMatchStatistics']);
    });
});
