import React, { useState } from 'react';
import { X, Trash2, <PERSON><PERSON><PERSON><PERSON>gle } from 'lucide-react';
import api from '@/lib/axios';
import toast from 'react-hot-toast';

interface MediaItem {
    id: number;
    title: string;
    description: string;
    file_url: string;
    file_urls?: string[];
    image_count?: number;
    file_type: 'image' | 'video';
    category: string;
    zone: string;
    views: number;
    uploaded_at: string;
    uploader?: string;
}

interface DeleteConfirmModalProps {
    isOpen: boolean;
    onClose: () => void;
    media: MediaItem | null;
    onSuccess: () => void;
}

const DeleteConfirmModal: React.FC<DeleteConfirmModalProps> = ({
    isOpen,
    onClose,
    media,
    onSuccess
}) => {
    const [isDeleting, setIsDeleting] = useState(false);

    if (!isOpen || !media) return null;

    const handleDelete = async () => {
        try {
            setIsDeleting(true);
            
            const response = await api.delete(`/cms/gallery/${media.id}`);
            
            if (response.data.success) {
                toast.success('Media deleted successfully!');
                onSuccess();
                onClose();
            } else {
                throw new Error(response.data.message || 'Failed to delete media');
            }
        } catch (error: any) {
            console.error('Delete error:', error);
            toast.error(error.response?.data?.message || 'Failed to delete media');
        } finally {
            setIsDeleting(false);
        }
    };

    const getImageCount = () => {
        if (media.file_urls && media.file_urls.length > 1) {
            return `${media.file_urls.length} images`;
        }
        return media.file_type === 'image' ? '1 image' : '1 video';
    };

    return (
        <div className="fixed inset-0 bg-black/80 backdrop-blur-sm z-50 flex items-center justify-center p-4">
            <div className="bg-white rounded-2xl max-w-md w-full">
                {/* Header */}
                <div className="p-6 border-b">
                    <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-3">
                            <div className="flex-shrink-0 w-10 h-10 bg-red-100 rounded-full flex items-center justify-center">
                                <AlertTriangle className="w-5 h-5 text-red-600" />
                            </div>
                            <h2 className="text-xl font-bold text-gray-900">
                                Confirm Delete
                            </h2>
                        </div>
                        <button
                            onClick={onClose}
                            disabled={isDeleting}
                            className="text-gray-400 hover:text-gray-600 transition-colors disabled:opacity-50"
                        >
                            <X className="w-6 h-6" />
                        </button>
                    </div>
                </div>

                {/* Content */}
                <div className="p-6">
                    <div className="space-y-4">
                        {/* Warning Message */}
                        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                            <div className="flex">
                                <div className="flex-shrink-0">
                                    <AlertTriangle className="h-5 w-5 text-red-400" />
                                </div>
                                <div className="ml-3">
                                    <h3 className="text-sm font-medium text-red-800">
                                        This action cannot be undone
                                    </h3>
                                    <div className="mt-2 text-sm text-red-700">
                                        <p>
                                            You are about to permanently delete this media item. 
                                            All associated data will be lost.
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        {/* Media Info */}
                        <div className="bg-gray-50 rounded-lg p-4">
                            <h4 className="font-semibold text-gray-900 mb-2">Media Details:</h4>
                            <div className="space-y-2 text-sm text-gray-600">
                                <div>
                                    <span className="font-medium">Title:</span> {media.title}
                                </div>
                                <div>
                                    <span className="font-medium">Type:</span> {getImageCount()}
                                </div>
                                <div>
                                    <span className="font-medium">Category:</span> {media.category}
                                </div>
                                <div>
                                    <span className="font-medium">Zone:</span> {media.zone}
                                </div>
                                <div>
                                    <span className="font-medium">Views:</span> {media.views.toLocaleString()}
                                </div>
                            </div>
                        </div>

                        {/* Confirmation Text */}
                        <div className="text-center">
                            <p className="text-gray-600">
                                Are you sure you want to delete <strong>"{media.title}"</strong>?
                            </p>
                        </div>
                    </div>
                </div>

                {/* Footer */}
                <div className="border-t p-6">
                    <div className="flex justify-end space-x-3">
                        <button
                            onClick={onClose}
                            disabled={isDeleting}
                            className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors disabled:opacity-50"
                        >
                            Cancel
                        </button>
                        <button
                            onClick={handleDelete}
                            disabled={isDeleting}
                            className="px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors disabled:opacity-50 flex items-center"
                        >
                            {isDeleting ? (
                                <>
                                    <div className="w-4 h-4 mr-2 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                                    Deleting...
                                </>
                            ) : (
                                <>
                                    <Trash2 className="w-4 h-4 mr-2" />
                                    Delete Media
                                </>
                            )}
                        </button>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default DeleteConfirmModal;
