import React from 'react';
import { X, Calendar, Eye, User, MapPin, Tag } from 'lucide-react';
import OptimizedImage from '@/components/OptimizedImage';

interface MediaItem {
    id: number;
    title: string;
    description: string;
    file_url: string;
    file_type: 'image' | 'video';
    category: string;
    zone: string;
    views: number;
    uploaded_at: string;
    uploader?: string;
}

interface ViewMediaModalProps {
    isOpen: boolean;
    onClose: () => void;
    media: MediaItem | null;
}

const ViewMediaModal: React.FC<ViewMediaModalProps> = ({
    isOpen,
    onClose,
    media
}) => {
    if (!isOpen || !media) return null;

    const formatDate = (dateString: string) => {
        return new Date(dateString).toLocaleDateString('ms-MY', {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
    };

    const getCategoryName = (category: string) => {
        const categories: { [key: string]: string } = {
            'tournament': 'Pertandingan',
            'training': 'La<PERSON>han',
            'ceremony': 'Majlis',
            'general': 'Am'
        };
        return categories[category] || category;
    };

    const getCategoryColor = (category: string) => {
        const colors: { [key: string]: string } = {
            'tournament': 'bg-red-500/20 text-red-400 border-red-500/30',
            'training': 'bg-blue-500/20 text-blue-400 border-blue-500/30',
            'ceremony': 'bg-purple-500/20 text-purple-400 border-purple-500/30',
            'general': 'bg-green-500/20 text-green-400 border-green-500/30'
        };
        return colors[category] || 'bg-gray-500/20 text-gray-400 border-gray-500/30';
    };

    return (
        <div className="fixed inset-0 bg-black/80 backdrop-blur-sm z-50 flex items-center justify-center p-4">
            <div className="bg-white rounded-2xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
                {/* Header */}
                <div className="sticky top-0 bg-white border-b p-6 rounded-t-2xl">
                    <div className="flex items-center justify-between">
                        <h2 className="text-2xl font-bold text-gray-900">
                            Lihat Media
                        </h2>
                        <button
                            onClick={onClose}
                            className="text-gray-400 hover:text-gray-600 transition-colors"
                        >
                            <X className="w-6 h-6" />
                        </button>
                    </div>
                </div>

                {/* Content */}
                <div className="p-6">
                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                        {/* Media Display */}
                        <div className="space-y-4">
                            <div className="relative rounded-xl overflow-hidden bg-gray-100">
                                {media.file_type === 'image' ? (
                                    <OptimizedImage
                                        src={media.file_url}
                                        alt={media.title}
                                        className="w-full h-80 object-cover"
                                        lazy={false}
                                        enableCache={true}
                                    />
                                ) : (
                                    <video
                                        src={media.file_url}
                                        controls
                                        className="w-full h-80 object-cover"
                                    />
                                )}
                            </div>

                            {/* Category Badge */}
                            <div className="flex justify-center">
                                <span className={`px-4 py-2 rounded-full text-sm font-semibold border ${getCategoryColor(media.category)}`}>
                                    <Tag className="h-4 w-4 inline mr-2" />
                                    {getCategoryName(media.category)}
                                </span>
                            </div>
                        </div>

                        {/* Media Info */}
                        <div className="space-y-6">
                            <div>
                                <h3 className="text-2xl font-bold text-gray-900 mb-2">
                                    {media.title}
                                </h3>
                                <p className="text-gray-600 leading-relaxed">
                                    {media.description}
                                </p>
                            </div>

                            {/* Metadata */}
                            <div className="space-y-4">
                                <div className="flex items-center gap-3 text-gray-600">
                                    <Calendar className="h-5 w-5" />
                                    <span>Dimuat naik: {formatDate(media.uploaded_at)}</span>
                                </div>

                                <div className="flex items-center gap-3 text-gray-600">
                                    <Eye className="h-5 w-5" />
                                    <span>{media.views} tontonan</span>
                                </div>

                                <div className="flex items-center gap-3 text-gray-600">
                                    <MapPin className="h-5 w-5" />
                                    <span>Zon {media.zone}</span>
                                </div>

                                {media.uploader && (
                                    <div className="flex items-center gap-3 text-gray-600">
                                        <User className="h-5 w-5" />
                                        <span>Dimuat naik oleh: {media.uploader}</span>
                                    </div>
                                )}
                            </div>

                            {/* Stats Card */}
                            <div className="bg-gray-50 rounded-xl p-4">
                                <h4 className="font-semibold text-gray-900 mb-3">Statistik Media</h4>
                                <div className="grid grid-cols-2 gap-4">
                                    <div className="text-center">
                                        <div className="text-2xl font-bold text-blue-600">{media.views}</div>
                                        <div className="text-sm text-gray-600">Tontonan</div>
                                    </div>
                                    <div className="text-center">
                                        <div className="text-2xl font-bold text-green-600">
                                            {media.file_type === 'image' ? 'Foto' : 'Video'}
                                        </div>
                                        <div className="text-sm text-gray-600">Jenis Media</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                {/* Footer */}
                <div className="border-t p-6 bg-gray-50 rounded-b-2xl">
                    <div className="flex justify-end">
                        <button
                            onClick={onClose}
                            className="px-6 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
                        >
                            Tutup
                        </button>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default ViewMediaModal;
