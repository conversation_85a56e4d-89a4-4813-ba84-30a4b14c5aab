import React, { useState, useEffect } from 'react';
import { X, Upload, Loader2, AlertCircle } from 'lucide-react';
import { z } from 'zod';
import api from '@/lib/axios';
import toast from 'react-hot-toast';

interface NewsItem {
    id: number;
    title: string;
    excerpt: string;
    content: string;
    featured_image?: string;
    category: string;
    tags: string[];
    is_important: boolean;
}

interface EditNewsModalProps {
    isOpen: boolean;
    onClose: () => void;
    onSuccess: () => void;
    news: NewsItem | null;
}

const newsSchema = z.object({
    title: z.string().min(1, 'Tajuk diperlukan').max(255, 'Tajuk terlalu panjang'),
    excerpt: z.string().min(1, 'Ringkasan diperlukan').max(500, 'Ringkasan terlalu panjang'),
    content: z.string().min(1, 'Kandungan diperlukan'),
    category: z.enum(['announcement', 'registration', 'schedule', 'results', 'tips', 'general']),
    tags: z.string().optional(),
    is_important: z.boolean(),
    featured_image: z.any().optional()
});

type NewsFormData = z.infer<typeof newsSchema>;

const EditNewsModal: React.FC<EditNewsModalProps> = ({
    isOpen,
    onClose,
    onSuccess,
    news
}) => {
    const [formData, setFormData] = useState<NewsFormData>({
        title: '',
        excerpt: '',
        content: '',
        category: 'general',
        tags: '',
        is_important: false,
        featured_image: null
    });
    const [errors, setErrors] = useState<Record<string, string>>({});
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [imagePreview, setImagePreview] = useState<string | null>(null);

    const categories = [
        { value: 'announcement', label: 'Pengumuman' },
        { value: 'registration', label: 'Pendaftaran' },
        { value: 'schedule', label: 'Jadual' },
        { value: 'results', label: 'Keputusan' },
        { value: 'tips', label: 'Tips' },
        { value: 'general', label: 'Am' }
    ];

    // Populate form when news data changes
    useEffect(() => {
        if (news && isOpen) {
            setFormData({
                title: news.title,
                excerpt: news.excerpt,
                content: news.content,
                category: news.category as any,
                tags: news.tags ? news.tags.join(', ') : '',
                is_important: news.is_important,
                featured_image: null
            });
            setImagePreview(news.featured_image || null);
        }
    }, [news, isOpen]);

    const handleInputChange = (field: keyof NewsFormData, value: any) => {
        setFormData(prev => ({ ...prev, [field]: value }));
        if (errors[field]) {
            setErrors(prev => ({ ...prev, [field]: '' }));
        }
    };

    const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const file = e.target.files?.[0];
        if (file) {
            setFormData(prev => ({ ...prev, featured_image: file }));
            
            // Create preview
            const reader = new FileReader();
            reader.onload = (e) => {
                setImagePreview(e.target?.result as string);
            };
            reader.readAsDataURL(file);
        }
    };

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        
        if (!news) return;

        try {
            // Validate form data
            const validatedData = newsSchema.parse(formData);
            setErrors({});
            setIsSubmitting(true);

            // Create FormData for file upload
            const submitData = new FormData();
            submitData.append('title', validatedData.title);
            submitData.append('excerpt', validatedData.excerpt);
            submitData.append('content', validatedData.content);
            submitData.append('category', validatedData.category);
            submitData.append('is_important', validatedData.is_important ? '1' : '0');
            submitData.append('_method', 'PUT');
            
            if (validatedData.tags) {
                const tagsArray = validatedData.tags.split(',').map(tag => tag.trim()).filter(tag => tag);
                submitData.append('tags', JSON.stringify(tagsArray));
            }
            
            if (validatedData.featured_image) {
                submitData.append('featured_image', validatedData.featured_image);
            }

            await api.post(`/cms/news/${news.id}`, submitData, {
                headers: {
                    'Content-Type': 'multipart/form-data',
                },
            });

            onSuccess();
            handleReset();
        } catch (error: any) {
            if (error instanceof z.ZodError) {
                const fieldErrors: Record<string, string> = {};
                error.errors.forEach((err) => {
                    if (err.path) {
                        fieldErrors[err.path[0]] = err.message;
                    }
                });
                setErrors(fieldErrors);
            } else {
                console.error('Failed to update news:', error);
                toast.error('Gagal mengemas kini berita');
            }
        } finally {
            setIsSubmitting(false);
        }
    };

    const handleReset = () => {
        setFormData({
            title: '',
            excerpt: '',
            content: '',
            category: 'general',
            tags: '',
            is_important: false,
            featured_image: null
        });
        setErrors({});
        setImagePreview(null);
    };

    const handleClose = () => {
        handleReset();
        onClose();
    };

    if (!isOpen || !news) return null;

    return (
        <div className="fixed inset-0 bg-black/80 backdrop-blur-sm z-50 flex items-center justify-center p-2 sm:p-4">
            <div className="bg-white rounded-2xl max-w-4xl w-full max-h-[95vh] flex flex-col">
                {/* Fixed Header */}
                <div className="flex-shrink-0 bg-white border-b p-4 sm:p-6 rounded-t-2xl">
                    <div className="flex items-center justify-between">
                        <h2 className="text-xl sm:text-2xl font-bold text-gray-900">
                            Edit Berita
                        </h2>
                        <button
                            onClick={handleClose}
                            className="text-gray-400 hover:text-gray-600 transition-colors p-1"
                        >
                            <X className="w-5 h-5 sm:w-6 sm:h-6" />
                        </button>
                    </div>
                </div>

                {/* Scrollable Content */}
                <div className="flex-1 overflow-y-auto p-4 sm:p-6">
                    <form onSubmit={handleSubmit} className="space-y-6">
                        {/* Title */}
                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">
                                Tajuk Berita *
                            </label>
                            <input
                                type="text"
                                value={formData.title}
                                onChange={(e) => handleInputChange('title', e.target.value)}
                                className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                                    errors.title ? 'border-red-500' : 'border-gray-300'
                                }`}
                                placeholder="Masukkan tajuk berita..."
                            />
                            {errors.title && (
                                <p className="mt-1 text-sm text-red-600 flex items-center gap-1">
                                    <AlertCircle className="w-4 h-4" />
                                    {errors.title}
                                </p>
                            )}
                        </div>

                        {/* Excerpt */}
                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">
                                Ringkasan *
                            </label>
                            <textarea
                                value={formData.excerpt}
                                onChange={(e) => handleInputChange('excerpt', e.target.value)}
                                rows={3}
                                className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                                    errors.excerpt ? 'border-red-500' : 'border-gray-300'
                                }`}
                                placeholder="Masukkan ringkasan berita..."
                            />
                            {errors.excerpt && (
                                <p className="mt-1 text-sm text-red-600 flex items-center gap-1">
                                    <AlertCircle className="w-4 h-4" />
                                    {errors.excerpt}
                                </p>
                            )}
                        </div>

                        {/* Content */}
                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">
                                Kandungan *
                            </label>
                            <textarea
                                value={formData.content}
                                onChange={(e) => handleInputChange('content', e.target.value)}
                                rows={8}
                                className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                                    errors.content ? 'border-red-500' : 'border-gray-300'
                                }`}
                                placeholder="Masukkan kandungan berita..."
                            />
                            {errors.content && (
                                <p className="mt-1 text-sm text-red-600 flex items-center gap-1">
                                    <AlertCircle className="w-4 h-4" />
                                    {errors.content}
                                </p>
                            )}
                        </div>

                        {/* Category and Important */}
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-2">
                                    Kategori *
                                </label>
                                <select
                                    value={formData.category}
                                    onChange={(e) => handleInputChange('category', e.target.value)}
                                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                >
                                    {categories.map(cat => (
                                        <option key={cat.value} value={cat.value}>
                                            {cat.label}
                                        </option>
                                    ))}
                                </select>
                            </div>

                            <div className="flex items-center">
                                <label className="flex items-center gap-3 cursor-pointer">
                                    <input
                                        type="checkbox"
                                        checked={formData.is_important}
                                        onChange={(e) => handleInputChange('is_important', e.target.checked)}
                                        className="w-5 h-5 text-red-600 border-gray-300 rounded focus:ring-red-500"
                                    />
                                    <span className="text-sm font-medium text-gray-700">
                                        Berita Penting
                                    </span>
                                </label>
                            </div>
                        </div>

                        {/* Tags */}
                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">
                                Tags (dipisahkan dengan koma)
                            </label>
                            <input
                                type="text"
                                value={formData.tags}
                                onChange={(e) => handleInputChange('tags', e.target.value)}
                                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                placeholder="contoh: sukan, pendaftaran, penting"
                            />
                        </div>

                        {/* Featured Image */}
                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">
                                Gambar Utama
                            </label>
                            {imagePreview ? (
                                <div className="space-y-4">
                                    <img
                                        src={imagePreview}
                                        alt="Preview"
                                        className="max-w-full h-48 object-cover mx-auto rounded-lg border"
                                    />
                                    <div className="flex gap-3 justify-center">
                                        <label className="cursor-pointer bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors text-sm">
                                            <Upload className="w-4 h-4 inline mr-2" />
                                            Tukar Gambar
                                            <input
                                                type="file"
                                                accept="image/*"
                                                onChange={handleImageChange}
                                                className="hidden"
                                            />
                                        </label>
                                        <button
                                            type="button"
                                            onClick={() => {
                                                setImagePreview(null);
                                                setFormData(prev => ({ ...prev, featured_image: null }));
                                            }}
                                            className="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors text-sm"
                                        >
                                            Buang Gambar
                                        </button>
                                    </div>
                                </div>
                            ) : (
                                <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center">
                                    <Upload className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                                    <p className="text-gray-600 mb-4">Pilih gambar baru untuk berita</p>
                                    <label className="cursor-pointer bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors inline-flex items-center gap-2">
                                        <Upload className="w-4 h-4" />
                                        Pilih Gambar
                                        <input
                                            type="file"
                                            accept="image/*"
                                            onChange={handleImageChange}
                                            className="hidden"
                                        />
                                    </label>
                                    <p className="text-sm text-gray-500 mt-2">PNG, JPG hingga 10MB</p>
                                </div>
                            )}
                        </div>
                    </form>
                </div>

                {/* Fixed Footer */}
                <div className="flex-shrink-0 border-t p-4 sm:p-6 bg-gray-50 rounded-b-2xl">
                    <div className="flex justify-end gap-3">
                        <button
                            type="button"
                            onClick={handleClose}
                            className="px-4 py-2 sm:px-6 sm:py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors text-sm sm:text-base"
                        >
                            Batal
                        </button>
                        <button
                            onClick={handleSubmit}
                            disabled={isSubmitting}
                            className="px-4 py-2 sm:px-6 sm:py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm sm:text-base disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2"
                        >
                            {isSubmitting && <Loader2 className="w-4 h-4 animate-spin" />}
                            {isSubmitting ? 'Menyimpan...' : 'Simpan Perubahan'}
                        </button>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default EditNewsModal;
