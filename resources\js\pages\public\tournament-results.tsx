import { useState, useEffect } from 'react';
import { Trophy, Medal, Crown, Star, Target, Users, Calendar, MapPin } from 'lucide-react';
import { usePageContent } from '@/hooks/usePageContent';
import OptimizedImage from '@/components/OptimizedImage';
import { SportLogo } from '@/components/ui/logo-components';

import publicApi from '@/lib/public-api';

// Types for tournament data
interface TournamentResult {
    id: number;
    sport: string;
    category: string;
    zone: string;
    date: string;
    status: 'completed' | 'ongoing' | 'upcoming' | 'cancelled';
    winner: string | null;
    runnerUp: string | null;
    thirdPlace: string | null;
    participants: number;
    matches: number;
    finalScore: string | null;
}

interface OverallStanding {
    position: number;
    zone: string;
    points: number;
    gold: number;
    silver: number;
    bronze: number;
}





const getStatusColor = (status: string) => {
    switch (status) {
        case 'completed':
            return 'bg-green-500/20 text-green-300 border-green-500/30';
        case 'ongoing':
            return 'bg-yellow-500/20 text-yellow-300 border-yellow-500/30';
        case 'upcoming':
            return 'bg-blue-500/20 text-blue-300 border-blue-500/30';
        default:
            return 'bg-gray-500/20 text-gray-300 border-gray-500/30';
    }
};

const getStatusText = (status: string) => {
    switch (status) {
        case 'completed':
            return 'Selesai';
        case 'ongoing':
            return 'Berlangsung';
        case 'upcoming':
            return 'Akan Datang';
        case 'cancelled':
            return 'Dibatalkan';
        default:
            return 'Tidak Diketahui';
    }
};

const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('ms-MY', {
        day: 'numeric',
        month: 'long',
        year: 'numeric'
    });
};

export default function TournamentResults() {
    // State for tournament data
    const [tournamentResults, setTournamentResults] = useState<TournamentResult[]>([]);
    const [overallStandings, setOverallStandings] = useState<OverallStanding[]>([]);
    const [isLoading, setIsLoading] = useState(true);

    // Get page content from CMS
    const { data: pageContent = {}, isLoading: contentLoading } = usePageContent('tournament-results');

    // Fetch tournament data
    useEffect(() => {
        fetchTournamentData();
    }, []);

    const fetchTournamentData = async () => {
        try {
            setIsLoading(true);

            // Fetch tournament results from API
            const [resultsResponse, standingsResponse] = await Promise.all([
                publicApi.get('/public/jadual/tournament-results'),
                publicApi.get('/public/jadual/overall-standings')
            ]);

            setTournamentResults(resultsResponse.data.data || []);
            setOverallStandings(standingsResponse.data.data || []);

        } catch (error) {
            console.error('Error fetching tournament data:', error);
            // Fallback to empty arrays
            setTournamentResults([]);
            setOverallStandings([]);
        } finally {
            setIsLoading(false);
        }
    };



    // Use filtered results
    const filteredResults = tournamentResults;

    // Get content from CMS or use defaults
    const heroContent = pageContent.hero || {};
    const backgroundImage = heroContent.background_image || "/images/default.jpg";
    const title = heroContent.title || "KEPUTUSAN KEJOHANAN";
    const subtitle = heroContent.subtitle || "Lihat keputusan lengkap semua pertandingan dan pencapaian terbaik dalam Sukan Intra Kor Kesihatan DiRaja 2025";
    const description = heroContent.description || "21 - 25 Julai 2025 | PU Sendayan";

    if (contentLoading || isLoading) {
        return (
            <div className="flex items-center justify-center min-h-screen bg-gray-900">
                <div className="text-center">
                    <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-yellow-400 mx-auto mb-4"></div>
                    <div className="text-lg text-white">Memuat keputusan kejohanan...</div>
                </div>
            </div>
        );
    }

    return (
        <div className="min-h-screen bg-gray-50">
            {/* Hero Section */}
            <section
                className="relative min-h-screen bg-cover bg-center bg-no-repeat pt-20 sm:pt-24 lg:pt-20 xl:pt-32"
                style={{
                    backgroundImage: `linear-gradient(rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0.5)), url('${backgroundImage}')`
                }}
            >
                <div className="container mx-auto max-w-7xl px-3 sm:px-6 lg:px-7 xl:px-8 py-8 sm:py-12 lg:py-8 xl:py-16">
                    <div className="text-center">
                        <h1 className="text-3xl sm:text-4xl lg:text-4xl xl:text-6xl font-black text-white mb-4 sm:mb-5 lg:mb-3 xl:mb-6 drop-shadow-lg">
                            <span className="text-yellow-400">{title.split(' ')[0] || 'KEPUTUSAN'}</span> {title.split(' ').slice(1).join(' ') || 'KEJOHANAN'}
                        </h1>
                        <p className="text-base sm:text-lg lg:text-base xl:text-xl text-white/90 mb-6 sm:mb-8 lg:mb-4 xl:mb-8 leading-relaxed lg:leading-snug xl:leading-relaxed max-w-4xl mx-auto">
                            {subtitle}
                            <br />
                            <span className="text-yellow-300 font-semibold">{description}</span>
                        </p>

                       
                    </div>
                </div>
            </section>



            {/* Overall Standings Section */}
            <section className="py-12 sm:py-16 lg:py-12 xl:py-20 bg-gradient-to-br from-gray-900 to-black">
                <div className="container mx-auto max-w-7xl px-3 sm:px-6 lg:px-7 xl:px-8">
                    <div className="text-center mb-8 sm:mb-12 lg:mb-8 xl:mb-16">
                        <h2 className="text-3xl sm:text-4xl lg:text-4xl xl:text-5xl font-black text-white mb-4 sm:mb-5 lg:mb-3 xl:mb-6">
                            <span className="text-yellow-400">KEDUDUKAN</span> KESELURUHAN
                        </h2>
                        <p className="text-base sm:text-lg lg:text-base xl:text-xl text-gray-300 max-w-3xl mx-auto">
                            Ranking zone berdasarkan jumlah mata yang diperoleh
                        </p>
                    </div>

                    <div className="max-w-4xl mx-auto">
                        <div className="bg-black/60 backdrop-blur-xl rounded-2xl sm:rounded-3xl lg:rounded-2xl xl:rounded-3xl border-2 border-yellow-400/50 overflow-hidden shadow-2xl">
                            <div className="bg-gradient-to-r from-blue-600/80 to-green-600/80 backdrop-blur-sm p-4 sm:p-5 lg:p-4 xl:p-6 border-b border-yellow-400/30">
                                <h3 className="text-xl sm:text-2xl lg:text-xl xl:text-3xl font-black text-white mb-2 lg:mb-1 xl:mb-2 drop-shadow-lg text-center">
                                    <span className="text-yellow-400">CARTA</span> KEDUDUKAN
                                </h3>
                                <p className="text-sm sm:text-base lg:text-sm xl:text-lg text-white/90 text-center">
                                    Berdasarkan pencapaian semua kategori sukan
                                </p>
                            </div>

                            <div className="p-4 sm:p-6 lg:p-4 xl:p-8">
                                <div className="space-y-4 sm:space-y-5 lg:space-y-4 xl:space-y-6">
                                    {overallStandings.length === 0 ? (
                                        <div className="text-center py-8">
                                            <Trophy className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                                            <p className="text-gray-400 text-lg">Kedudukan akan dikemaskini selepas pertandingan selesai</p>
                                        </div>
                                    ) : (
                                        overallStandings.map((standing) => (
                                        <div key={standing.position} className={`flex flex-col sm:flex-row sm:items-center sm:justify-between p-4 sm:p-5 lg:p-4 xl:p-6 rounded-xl border transition-all duration-300 hover:scale-105 space-y-3 sm:space-y-0 ${
                                            standing.position === 1 ? 'bg-gradient-to-r from-yellow-500/20 to-orange-500/20 border-yellow-400/50' :
                                            standing.position === 2 ? 'bg-gradient-to-r from-gray-400/20 to-gray-500/20 border-gray-400/50' :
                                            standing.position === 3 ? 'bg-gradient-to-r from-amber-600/20 to-amber-700/20 border-amber-600/50' :
                                            'bg-white/10 border-white/20'
                                        }`}>
                                            <div className="flex items-center space-x-4 sm:space-x-5 lg:space-x-4 xl:space-x-6">
                                                <div className="flex items-center space-x-2 sm:space-x-3 lg:space-x-2 xl:space-x-3">
                                                    {standing.position === 1 && <Crown className="h-8 w-8 sm:h-9 sm:w-9 lg:h-8 lg:w-8 xl:h-10 xl:w-10 text-yellow-400" />}
                                                    {standing.position === 2 && <Medal className="h-8 w-8 sm:h-9 sm:w-9 lg:h-8 lg:w-8 xl:h-10 xl:w-10 text-gray-400" />}
                                                    {standing.position === 3 && <Medal className="h-8 w-8 sm:h-9 sm:w-9 lg:h-8 lg:w-8 xl:h-10 xl:w-10 text-amber-600" />}
                                                    {standing.position > 3 && <Star className="h-8 w-8 sm:h-9 sm:w-9 lg:h-8 lg:w-8 xl:h-10 xl:w-10 text-gray-400" />}
                                                    <span className="text-2xl sm:text-3xl lg:text-2xl xl:text-4xl font-black text-white">#{standing.position}</span>
                                                </div>
                                                <div>
                                                    <h3 className="text-lg sm:text-xl lg:text-lg xl:text-2xl font-black text-white">{standing.zone}</h3>
                                                    <p className="text-sm sm:text-base lg:text-sm xl:text-lg text-white/70">{standing.points} mata</p>
                                                </div>
                                            </div>
                                            <div className="flex items-center justify-center sm:justify-end space-x-4 sm:space-x-6 lg:space-x-4 xl:space-x-8">
                                                <div className="text-center">
                                                    <div className="flex items-center space-x-1 sm:space-x-2 lg:space-x-1 xl:space-x-2">
                                                        <Medal className="h-4 w-4 sm:h-5 sm:w-5 lg:h-4 lg:w-4 xl:h-6 xl:w-6 text-yellow-400" />
                                                        <span className="text-lg sm:text-xl lg:text-lg xl:text-2xl font-bold text-yellow-400">{standing.gold}</span>
                                                    </div>
                                                    <p className="text-xs sm:text-sm lg:text-xs xl:text-sm text-white/70">Emas</p>
                                                </div>
                                                <div className="text-center">
                                                    <div className="flex items-center space-x-1 sm:space-x-2 lg:space-x-1 xl:space-x-2">
                                                        <Medal className="h-4 w-4 sm:h-5 sm:w-5 lg:h-4 lg:w-4 xl:h-6 xl:w-6 text-gray-400" />
                                                        <span className="text-lg sm:text-xl lg:text-lg xl:text-2xl font-bold text-gray-400">{standing.silver}</span>
                                                    </div>
                                                    <p className="text-xs sm:text-sm lg:text-xs xl:text-sm text-white/70">Perak</p>
                                                </div>
                                                <div className="text-center">
                                                    <div className="flex items-center space-x-1 sm:space-x-2 lg:space-x-1 xl:space-x-2">
                                                        <Medal className="h-4 w-4 sm:h-5 sm:w-5 lg:h-4 lg:w-4 xl:h-6 xl:w-6 text-amber-600" />
                                                        <span className="text-lg sm:text-xl lg:text-lg xl:text-2xl font-bold text-amber-600">{standing.bronze}</span>
                                                    </div>
                                                    <p className="text-xs sm:text-sm lg:text-xs xl:text-sm text-white/70">Gangsa</p>
                                                </div>
                                            </div>
                                        </div>
                                        ))
                                    )}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            {/* Tournament Results Section */}
            <section className="py-12 sm:py-16 lg:py-12 xl:py-20 bg-gradient-to-br from-black to-gray-900">
                <div className="container mx-auto max-w-7xl px-3 sm:px-6 lg:px-7 xl:px-8">
                    <div className="text-center mb-8 sm:mb-12 lg:mb-8 xl:mb-16">
                        <h2 className="text-3xl sm:text-4xl lg:text-4xl xl:text-5xl font-black text-white mb-4 sm:mb-5 lg:mb-3 xl:mb-6">
                            <span className="text-yellow-400">KEPUTUSAN</span> PERTANDINGAN
                        </h2>
                        <p className="text-base sm:text-lg lg:text-base xl:text-xl text-gray-300 max-w-3xl mx-auto">
                            {filteredResults.length} keputusan dijumpai
                        </p>
                    </div>

                    <div className="max-w-6xl mx-auto">
                        {filteredResults.length === 0 ? (
                            <div className="bg-black/60 backdrop-blur-xl rounded-2xl sm:rounded-3xl lg:rounded-2xl xl:rounded-3xl border-2 border-yellow-400/50 p-8 sm:p-12 lg:p-8 xl:p-16 text-center">
                                <Trophy className="w-12 h-12 sm:w-14 sm:h-14 lg:w-12 lg:h-12 xl:w-16 xl:h-16 text-white/30 mx-auto mb-3 sm:mb-4 lg:mb-3 xl:mb-4" />
                                <h3 className="text-lg sm:text-xl lg:text-lg xl:text-xl font-bold text-white mb-2">Tiada keputusan dijumpai</h3>
                                <p className="text-sm sm:text-base lg:text-sm xl:text-base text-white/70">
                                    Cuba ubah kriteria filter anda
                                </p>
                            </div>
                        ) : (
                            <div className="space-y-6 sm:space-y-7 lg:space-y-5 xl:space-y-8">
                                {filteredResults.map(result => (
                                    <div key={result.id} className="bg-black/60 backdrop-blur-xl rounded-2xl sm:rounded-3xl lg:rounded-2xl xl:rounded-3xl border-2 border-yellow-400/50 overflow-hidden shadow-2xl">
                                        <div className="bg-gradient-to-r from-blue-600/80 to-green-600/80 backdrop-blur-sm p-4 sm:p-5 lg:p-4 xl:p-6 border-b border-yellow-400/30">
                                            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-3 sm:space-y-0">
                                                <div className="flex items-center space-x-3 sm:space-x-4 lg:space-x-3 xl:space-x-4">
                                                    <SportLogo
                                                        sportName={result.sport}
                                                        logoUrl={(result as any).sport_logo ? `${window.location.origin}${(result as any).sport_logo}` : undefined}
                                                        className="w-10 h-10 sm:w-12 sm:h-12 lg:w-10 lg:h-10 xl:w-12 xl:h-12 object-contain"
                                                    />
                                                    <div>
                                                        <h3 className="text-lg sm:text-xl lg:text-lg xl:text-2xl font-black text-white">
                                                            {result.sport}
                                                        </h3>
                                                        <p className="text-sm sm:text-base lg:text-sm xl:text-base text-white/80">
                                                            {result.category} • {result.zone}
                                                        </p>
                                                    </div>
                                                </div>
                                                <div className="text-center sm:text-right">
                                                    <span className={`px-3 py-1 sm:px-4 sm:py-2 lg:px-3 lg:py-1 xl:px-4 xl:py-2 rounded-full text-xs sm:text-sm lg:text-xs xl:text-sm font-semibold border ${getStatusColor(result.status)}`}>
                                                        {getStatusText(result.status)}
                                                    </span>
                                                    <p className="text-white/70 text-xs sm:text-sm lg:text-xs xl:text-sm mt-1">
                                                        {formatDate(result.date)}
                                                    </p>
                                                </div>
                                            </div>
                                        </div>

                                        <div className="p-4 sm:p-6 lg:p-4 xl:p-8">
                                            {result.status === 'completed' ? (
                                                <div>
                                                    <div className="grid grid-cols-1 sm:grid-cols-1 lg:grid-cols-3 xl:grid-cols-3 gap-4 sm:gap-5 lg:gap-4 xl:gap-6 mb-4 sm:mb-5 lg:mb-4 xl:mb-6">
                                                        <div className="text-center p-4 sm:p-5 lg:p-4 xl:p-6 bg-gradient-to-br from-yellow-500/20 to-orange-500/20 rounded-xl border border-yellow-400/30">
                                                            <Crown className="h-8 w-8 sm:h-10 sm:w-10 lg:h-8 lg:w-8 xl:h-12 xl:w-12 text-yellow-400 mx-auto mb-2 sm:mb-3 lg:mb-2 xl:mb-3" />
                                                            <h4 className="text-sm sm:text-base lg:text-sm xl:text-xl font-bold text-yellow-400 mb-1 sm:mb-2 lg:mb-1 xl:mb-2">JUARA</h4>
                                                            <p className="text-white text-sm sm:text-base lg:text-sm xl:text-lg font-semibold">{result.winner}</p>
                                                        </div>
                                                        <div className="text-center p-4 sm:p-5 lg:p-4 xl:p-6 bg-gradient-to-br from-gray-400/20 to-gray-500/20 rounded-xl border border-gray-400/30">
                                                            <Medal className="h-8 w-8 sm:h-10 sm:w-10 lg:h-8 lg:w-8 xl:h-12 xl:w-12 text-gray-400 mx-auto mb-2 sm:mb-3 lg:mb-2 xl:mb-3" />
                                                            <h4 className="text-sm sm:text-base lg:text-sm xl:text-xl font-bold text-gray-400 mb-1 sm:mb-2 lg:mb-1 xl:mb-2">NAIB JUARA</h4>
                                                            <p className="text-white text-sm sm:text-base lg:text-sm xl:text-lg font-semibold">{result.runnerUp}</p>
                                                        </div>
                                                        <div className="text-center p-4 sm:p-5 lg:p-4 xl:p-6 bg-gradient-to-br from-amber-600/20 to-amber-700/20 rounded-xl border border-amber-600/30">
                                                            <Medal className="h-8 w-8 sm:h-10 sm:w-10 lg:h-8 lg:w-8 xl:h-12 xl:w-12 text-amber-600 mx-auto mb-2 sm:mb-3 lg:mb-2 xl:mb-3" />
                                                            <h4 className="text-sm sm:text-base lg:text-sm xl:text-xl font-bold text-amber-600 mb-1 sm:mb-2 lg:mb-1 xl:mb-2">TEMPAT KETIGA</h4>
                                                            <p className="text-white text-sm sm:text-base lg:text-sm xl:text-lg font-semibold">{result.thirdPlace}</p>
                                                        </div>
                                                    </div>

                                                    {result.finalScore && (
                                                        <div className="text-center bg-white/10 backdrop-blur-sm rounded-xl p-3 sm:p-4 lg:p-3 xl:p-4 border border-white/20">
                                                            <p className="text-white/70 text-xs sm:text-sm lg:text-xs xl:text-sm mb-1">Keputusan Akhir:</p>
                                                            <p className="text-lg sm:text-xl lg:text-lg xl:text-2xl font-bold text-white">{result.finalScore}</p>
                                                        </div>
                                                    )}
                                                </div>
                                            ) : result.status === 'ongoing' ? (
                                                <div className="text-center py-6 sm:py-8 lg:py-6 xl:py-8">
                                                    <Target className="h-12 w-12 sm:h-14 sm:w-14 lg:h-12 lg:w-12 xl:h-16 xl:w-16 text-yellow-400 mx-auto mb-3 sm:mb-4 lg:mb-3 xl:mb-4" />
                                                    <h4 className="text-lg sm:text-xl lg:text-lg xl:text-xl font-bold text-white mb-2">Pertandingan Sedang Berlangsung</h4>
                                                    <p className="text-sm sm:text-base lg:text-sm xl:text-base text-white/70">
                                                        Keputusan akan dikemaskini selepas pertandingan selesai
                                                    </p>
                                                </div>
                                            ) : (
                                                <div className="text-center py-6 sm:py-8 lg:py-6 xl:py-8">
                                                    <Calendar className="h-12 w-12 sm:h-14 sm:w-14 lg:h-12 lg:w-12 xl:h-16 xl:w-16 text-blue-400 mx-auto mb-3 sm:mb-4 lg:mb-3 xl:mb-4" />
                                                    <h4 className="text-lg sm:text-xl lg:text-lg xl:text-xl font-bold text-white mb-2">Pertandingan Akan Datang</h4>
                                                    <p className="text-sm sm:text-base lg:text-sm xl:text-base text-white/70">
                                                        Sila rujuk jadual pertandingan untuk maklumat lanjut
                                                    </p>
                                                </div>
                                            )}

                                            <div className="grid grid-cols-1 sm:grid-cols-3 gap-3 sm:gap-4 lg:gap-3 xl:gap-4 mt-4 sm:mt-5 lg:mt-4 xl:mt-6 pt-4 sm:pt-5 lg:pt-4 xl:pt-6 border-t border-white/20">
                                                <div className="text-center">
                                                    <Users className="h-5 w-5 sm:h-6 sm:w-6 lg:h-5 lg:w-5 xl:h-6 xl:w-6 text-blue-400 mx-auto mb-1" />
                                                    <p className="text-white/70 text-xs sm:text-sm lg:text-xs xl:text-sm">Peserta</p>
                                                    <p className="text-white font-semibold text-sm sm:text-base lg:text-sm xl:text-base">{result.participants}</p>
                                                </div>
                                                <div className="text-center">
                                                    <Target className="h-5 w-5 sm:h-6 sm:w-6 lg:h-5 lg:w-5 xl:h-6 xl:w-6 text-green-400 mx-auto mb-1" />
                                                    <p className="text-white/70 text-xs sm:text-sm lg:text-xs xl:text-sm">Perlawanan</p>
                                                    <p className="text-white font-semibold text-sm sm:text-base lg:text-sm xl:text-base">{result.matches}</p>
                                                </div>
                                                <div className="text-center">
                                                    <MapPin className="h-5 w-5 sm:h-6 sm:w-6 lg:h-5 lg:w-5 xl:h-6 xl:w-6 text-yellow-400 mx-auto mb-1" />
                                                    <p className="text-white/70 text-xs sm:text-sm lg:text-xs xl:text-sm">Zone</p>
                                                    <p className="text-white font-semibold text-sm sm:text-base lg:text-sm xl:text-base">{result.zone}</p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                ))}
                            </div>
                        )}
                    </div>
                </div>
            </section>

        </div>
    );
}

