import { Link } from 'react-router-dom';
import { Users, Target, Calendar, Trophy, Camera, Newspaper, BookOpen, Share2 } from 'lucide-react';

interface Feature {
    id: string;
    title: string;
    description: string;
    icon: string;
    color: string;
    href?: string;
}

interface FeaturesGridProps {
    title?: string;
    subtitle?: string;
    features?: Feature[];
}

const defaultFeatures: Feature[] = [
    {
        id: 'match-schedule',
        title: 'Jadual Pertandingan',
        description: 'Jadual lengkap pertandingan dan venue',
        icon: 'calendar',
        color: 'teal',
        href: '/match-schedule'
    },
    {
        id: 'tournament-results',
        title: 'Keputusan Perlawanan',
        description: 'Hasil pertandingan dan ranking terkini',
        icon: 'trophy',
        color: 'blue',
        href: '/tournament-results'
    },
    {
        id: 'gallery',
        title: 'Galeri',
        description: 'Koleksi foto dan video acara sukan',
        icon: 'camera',
        color: 'yellow',
        href: '/gallery'
    },
    {
        id: 'news',
        title: 'Berita',
        description: 'Berita terkini dan pengumuman penting',
        icon: 'newspaper',
        color: 'orange',
        href: '/news'
    },
    {
        id: 'rules',
        title: 'Undang-undang Pertandingan',
        description: 'Peraturan dan syarat penyertaan',
        icon: 'book-open',
        color: 'pink',
        href: '/rules'
    },
    {
        id: 'social-media',
        title: 'Link Sosial Media KKD',
        description: 'Ikuti kami di media sosial dan live updates',
        icon: 'share2',
        color: 'cyan',
        href: '/social-media'
    }
];

const getIcon = (iconName: string) => {
    const icons: { [key: string]: any } = {
        users: Users,
        target: Target,
        calendar: Calendar,
        trophy: Trophy,
        camera: Camera,
        newspaper: Newspaper,
        'book-open': BookOpen,
        share2: Share2
    };
    return icons[iconName] || Users;
};

const getColorClasses = (color: string) => {
    const colors: { [key: string]: string } = {
        red: 'bg-red-100 group-hover:bg-red-200 text-red-600',
        purple: 'bg-purple-100 group-hover:bg-purple-200 text-purple-600',
        teal: 'bg-teal-100 group-hover:bg-teal-200 text-teal-600',
        blue: 'bg-blue-100 group-hover:bg-blue-200 text-blue-600',
        yellow: 'bg-yellow-100 group-hover:bg-yellow-200 text-yellow-600',
        orange: 'bg-orange-100 group-hover:bg-orange-200 text-orange-600',
        pink: 'bg-pink-100 group-hover:bg-pink-200 text-pink-600',
        cyan: 'bg-cyan-100 group-hover:bg-cyan-200 text-cyan-600'
    };
    return colors[color] || colors.blue;
};

export default function FeaturesGrid({
    title = "Kemudahan Portal",
    subtitle = "Platform lengkap untuk menguruskan semua aspek sukan intra-zon",
    features = defaultFeatures
}: FeaturesGridProps) {
    return (
        <section className="py-20 bg-gradient-to-br from-gray-50 to-blue-50">
            <div className="container mx-auto px-6">
                <div className="text-center mb-16">
                    <h2 className="text-4xl font-bold text-blue-900 mb-4">{title}</h2>
                    <p className="text-xl text-gray-700 max-w-3xl mx-auto">
                        {subtitle}
                    </p>
                </div>

                {/* Features Grid */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
                    {features.map((feature) => {
                        const IconComponent = getIcon(feature.icon);
                        const colorClasses = getColorClasses(feature.color);

                        const CardComponent = feature.href ? Link : 'div';
                        const cardProps = feature.href ? { href: feature.href } : {};

                        return (
                            <CardComponent
                                key={feature.id}
                                {...cardProps}
                                className="block group"
                            >
                                <div className="bg-white hover:bg-blue-50 border-2 border-blue-100 hover:border-blue-300 rounded-2xl p-8 text-center transition-all duration-300 transform hover:scale-105 hover:shadow-2xl h-full">
                                    <div className={`w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-6 transition-colors ${colorClasses} group-hover:scale-110`}>
                                        <IconComponent className="h-8 w-8" />
                                    </div>
                                    <h3 className="text-blue-900 text-xl font-bold mb-3 group-hover:text-blue-700 transition-colors">
                                        {feature.title}
                                    </h3>
                                    <p className="text-gray-600 group-hover:text-gray-700 transition-colors">
                                        {feature.description}
                                    </p>
                                </div>
                            </CardComponent>
                        );
                    })}
                </div>
            </div>
        </section>
    );
}
