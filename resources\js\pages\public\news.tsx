import { useState } from 'react';
import { Newspaper, Calendar, Eye, Tag, AlertCircle } from 'lucide-react';
import { usePageContent } from '@/hooks/usePageContent';
import OptimizedImage from '@/components/OptimizedImage';

interface NewsArticle {
    id: number;
    title: string;
    excerpt: string;
    content: string;
    featuredImage?: string;
    author: string;
    publishedAt: string;
    category: string;
    tags: string[];
    views: number;
    isImportant: boolean;
    zone: string;
}

interface NewsProps {
    newsArticles: NewsArticle[];
}

// Mock data for development
const newsArticles: NewsArticle[] = [
    {
        id: 1,
        title: "Pendaftaran Sukan Intra KKD 2025 Kini Dibuka",
        excerpt: "Pendaftaran untuk Sukan Intra Kor Kesihatan DiRaja 2025 telah dibuka secara rasmi. Jangan lepaskan peluang untuk menyertai acara sukan yang meriah ini.",
        content: "Lorem ipsum dolor sit amet...",
        featuredImage: "/images/badmintonpsd.png",
        author: "Admin Portal",
        publishedAt: "2025-01-15T10:00:00Z",
        category: "announcement",
        tags: ["pendaftaran", "sukan", "2025"],
        views: 1250,
        isImportant: true,
        zone: "Semua Zone"
    },
    {
        id: 2,
        title: "Jadual Pertandingan Badminton Telah Dikeluarkan",
        excerpt: "Jadual lengkap pertandingan badminton untuk semua kategori telah siap. Sila semak jadual anda dan pastikan kehadiran tepat pada masa.",
        content: "Lorem ipsum dolor sit amet...",
        featuredImage: "/images/badmintonpsd.png",
        author: "Pengurus Sukan",
        publishedAt: "2025-01-14T14:30:00Z",
        category: "schedule",
        tags: ["badminton", "jadual", "pertandingan"],
        views: 890,
        isImportant: false,
        zone: "Zone A"
    }
];

const categories = [
    { id: 'announcement', name: 'Pengumuman', color: 'bg-red-500/20 text-red-300 border-red-500/30' },
    { id: 'registration', name: 'Pendaftaran', color: 'bg-blue-500/20 text-blue-300 border-blue-500/30' },
    { id: 'schedule', name: 'Jadual', color: 'bg-green-500/20 text-green-300 border-green-500/30' },
    { id: 'results', name: 'Keputusan', color: 'bg-yellow-500/20 text-yellow-300 border-yellow-500/30' },
    { id: 'tips', name: 'Tips & Panduan', color: 'bg-purple-500/20 text-purple-300 border-purple-500/30' },
    { id: 'rules', name: 'Peraturan', color: 'bg-orange-500/20 text-orange-300 border-orange-500/30' },
    { id: 'updates', name: 'Kemaskini', color: 'bg-teal-500/20 text-teal-300 border-teal-500/30' }
];

const zones = [
    'Semua Zone',
    'Zone A',
    'Zone B',
    'Zone C'
];

const getCategoryColor = (categoryId: string) => {
    const category = categories.find(c => c.id === categoryId);
    return category?.color || 'bg-gray-500/20 text-gray-300 border-gray-500/30';
};

const getCategoryName = (categoryId: string) => {
    const category = categories.find(c => c.id === categoryId);
    return category?.name || categoryId;
};

const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('ms-MY', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    });
};

const timeAgo = (dateString: string) => {
    const now = new Date();
    const date = new Date(dateString);
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));

    if (diffInHours < 1) return 'Baru sahaja';
    if (diffInHours < 24) return `${diffInHours} jam yang lalu`;
    const diffInDays = Math.floor(diffInHours / 24);
    if (diffInDays < 7) return `${diffInDays} hari yang lalu`;
    return formatDate(dateString);
};

export default function News() {
    const [selectedCategory, setSelectedCategory] = useState<string>('all');
    const { data: content = {}, isLoading: loading } = usePageContent('news');

    const importantNews = newsArticles.filter(article => article.isImportant);
    const totalViews = newsArticles.reduce((sum, article) => sum + article.views, 0);

    // Get CMS content with fallbacks
    const heroTitle = content?.hero?.title || 'BERITA TERKINI';
    const heroSubtitle = content?.hero?.subtitle || 'Ikuti perkembangan terbaru dan pengumuman penting Sukan Intra Kor Kesihatan DiRaja 2025';
    const heroDescription = content?.hero?.description || 'Kekal terkini dengan semua maklumat, pengumuman rasmi, dan perkembangan terbaru mengenai Sukan Intra Kor Kesihatan DiRaja 2025.';
    const heroBackgroundImage = content?.hero?.background_image || '/images/default.jpg';

    return (
        <div className="min-h-screen bg-gray-50">
            {/* Hero Section */}
            <section
                className="relative min-h-screen bg-cover bg-center bg-no-repeat pt-32 2xl:pt-40 xl:pt-36 lg:pt-28 md:pt-28 sm:pt-24"
                style={{
                    backgroundImage: `linear-gradient(rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0.5)), url('${heroBackgroundImage}')`
                }}
            >
                <div className="container mx-auto px-6 2xl:px-8 xl:px-6 lg:px-4 md:px-4 sm:px-4 py-16 2xl:py-20 xl:py-18 lg:py-12 md:py-12 sm:py-8">
                    <div className="2xl:max-w-7xl xl:max-w-7xl lg:max-w-5xl md:max-w-5xl sm:max-w-4xl max-w-full mx-auto text-center">
                        <div className="inline-flex items-center justify-center w-20 h-20 2xl:w-24 2xl:h-24 xl:w-22 xl:h-22 lg:w-16 lg:h-16 md:w-18 md:h-18 sm:w-16 sm:h-16 bg-yellow-500/20 rounded-full mb-6 2xl:mb-8 xl:mb-7 lg:mb-4 md:mb-5 sm:mb-4">
                            <Newspaper className="w-10 h-10 2xl:w-12 2xl:h-12 xl:w-11 xl:h-11 lg:w-8 lg:h-8 md:w-9 md:h-9 sm:w-8 sm:h-8 text-yellow-400" />
                        </div>
                        <h1 className="text-5xl md:text-6xl 2xl:text-8xl xl:text-7xl lg:text-4xl sm:text-4xl font-black text-white mb-6 2xl:mb-8 xl:mb-7 lg:mb-4 md:mb-5 sm:mb-4 drop-shadow-lg">
                            <span className="text-yellow-400">{heroTitle}</span>
                        </h1>
                        <p className="text-xl 2xl:text-2xl xl:text-xl lg:text-base md:text-base sm:text-sm text-white/90 mb-6 2xl:mb-8 xl:mb-7 lg:mb-4 md:mb-5 sm:mb-4 leading-relaxed max-w-3xl mx-auto">
                            {heroSubtitle}
                        </p>
                        <p className="text-lg 2xl:text-xl xl:text-lg lg:text-sm md:text-base sm:text-sm text-white/80 mb-12 2xl:mb-16 xl:mb-14 lg:mb-8 md:mb-10 sm:mb-8 leading-relaxed max-w-4xl mx-auto">
                            {heroDescription}
                        </p>

                      
                    </div>
                </div>
            </section>



            {/* News Section */}
            <section className="py-20 2xl:py-28 xl:py-24 lg:py-12 md:py-16 sm:py-12 bg-gradient-to-br from-gray-900 to-black">
                <div className="container mx-auto px-6 2xl:px-8 xl:px-6 lg:px-4 md:px-4 sm:px-4 2xl:max-w-7xl xl:max-w-7xl lg:max-w-5xl md:max-w-5xl sm:max-w-4xl max-w-full">
                    <div className="text-center mb-16 2xl:mb-20 xl:mb-18 lg:mb-8 md:mb-12 sm:mb-8">
                        <h2 className="text-5xl 2xl:text-7xl xl:text-6xl lg:text-3xl md:text-4xl sm:text-3xl font-black text-white mb-6 2xl:mb-8 xl:mb-7 lg:mb-4 md:mb-5 sm:mb-4">
                            <span className="text-yellow-400">BERITA</span> TERKINI
                        </h2>
                        <p className="text-xl 2xl:text-2xl xl:text-xl lg:text-sm md:text-base sm:text-sm text-gray-300 max-w-3xl mx-auto">
                            {newsArticles.length} artikel berita tersedia
                        </p>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 2xl:grid-cols-4 xl:grid-cols-3 gap-8 2xl:gap-10 xl:gap-8 lg:gap-6 md:gap-6 sm:gap-4">
                        {newsArticles.map(article => (
                            <div key={article.id} className={`group bg-black/60 backdrop-blur-xl rounded-3xl 2xl:rounded-3xl xl:rounded-2xl lg:rounded-2xl md:rounded-xl sm:rounded-lg border-2 ${article.isImportant ? 'border-red-500/50 hover:border-red-500/70' : 'border-yellow-400/30 hover:border-yellow-400/60'} overflow-hidden shadow-2xl transition-all duration-300 hover:scale-105`}>
                                <div className="relative overflow-hidden">
                                    <img
                                        src={article.featuredImage}
                                        alt={article.title}
                                        className="w-full h-64 2xl:h-80 xl:h-72 lg:h-64 md:h-56 sm:h-48 object-cover group-hover:scale-110 transition-transform duration-500"
                                    />
                                    <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-colors duration-300"></div>

                                    {/* Important Badge */}
                                    {article.isImportant && (
                                        <div className="absolute top-4 left-4">
                                            <span className="px-3 py-1 2xl:px-4 2xl:py-2 xl:px-3 xl:py-1 lg:px-2 lg:py-1 md:px-2 md:py-1 sm:px-2 sm:py-1 rounded-full text-xs 2xl:text-sm xl:text-xs lg:text-xs md:text-xs sm:text-xs font-bold bg-red-500/90 text-white border border-red-400">
                                                <AlertCircle className="h-3 w-3 2xl:h-4 2xl:w-4 xl:h-3 xl:w-3 lg:h-3 lg:w-3 md:h-3 md:w-3 sm:h-3 sm:w-3 inline mr-1" />
                                                PENTING
                                            </span>
                                        </div>
                                    )}

                                    {/* Category Badge */}
                                    <div className="absolute top-4 right-4">
                                        <span className={`px-3 py-1 2xl:px-4 2xl:py-2 xl:px-3 xl:py-1 lg:px-2 lg:py-1 md:px-2 md:py-1 sm:px-2 sm:py-1 rounded-full text-xs 2xl:text-sm xl:text-xs lg:text-xs md:text-xs sm:text-xs font-bold border ${getCategoryColor(article.category)}`}>
                                            {getCategoryName(article.category)}
                                        </span>
                                    </div>
                                </div>

                                <div className="p-6 2xl:p-8 xl:p-7 lg:p-6 md:p-5 sm:p-4">
                                    <h3 className="text-xl 2xl:text-2xl xl:text-xl lg:text-lg md:text-base sm:text-sm font-bold text-white mb-3 2xl:mb-4 xl:mb-3 lg:mb-2 md:mb-2 sm:mb-2 group-hover:text-yellow-400 transition-colors cursor-pointer line-clamp-2">
                                        {article.title}
                                    </h3>

                                    <p className="text-gray-300 mb-4 2xl:mb-6 xl:mb-5 lg:mb-4 md:mb-3 sm:mb-3 line-clamp-3 text-sm 2xl:text-base xl:text-sm lg:text-xs md:text-xs sm:text-xs leading-relaxed">
                                        {article.excerpt}
                                    </p>

                                    <div className="flex items-center justify-between text-xs 2xl:text-sm xl:text-xs lg:text-xs md:text-xs sm:text-xs text-gray-400 mb-4 2xl:mb-6 xl:mb-5 lg:mb-4 md:mb-3 sm:mb-3">
                                        <div className="flex items-center space-x-2 2xl:space-x-3 xl:space-x-2 lg:space-x-1 md:space-x-1 sm:space-x-1">
                                            <Calendar className="h-3 w-3 2xl:h-4 2xl:w-4 xl:h-3 xl:w-3 lg:h-3 lg:w-3 md:h-3 md:w-3 sm:h-3 sm:w-3" />
                                            <span>{timeAgo(article.publishedAt)}</span>
                                        </div>
                                        <div className="flex items-center space-x-1">
                                            <Eye className="h-3 w-3 2xl:h-4 2xl:w-4 xl:h-3 xl:w-3 lg:h-3 lg:w-3 md:h-3 md:w-3 sm:h-3 sm:w-3" />
                                            <span>{article.views}</span>
                                        </div>
                                    </div>

                                    <div className="flex flex-wrap gap-1 2xl:gap-2 xl:gap-1 lg:gap-1 md:gap-1 sm:gap-1 mb-4 2xl:mb-6 xl:mb-5 lg:mb-4 md:mb-3 sm:mb-3">
                                        {article.tags.slice(0, 2).map(tag => (
                                            <span key={tag} className="px-2 py-1 2xl:px-3 2xl:py-1 xl:px-2 xl:py-1 lg:px-2 lg:py-1 md:px-2 md:py-1 sm:px-2 sm:py-1 bg-white/10 text-white/70 rounded text-xs 2xl:text-sm xl:text-xs lg:text-xs md:text-xs sm:text-xs">
                                                #{tag}
                                            </span>
                                        ))}
                                    </div>

                                    <button className="w-full bg-yellow-500/20 hover:bg-yellow-500/30 text-yellow-300 px-4 py-2 2xl:px-6 2xl:py-3 xl:px-5 xl:py-2 lg:px-4 lg:py-2 md:px-4 md:py-2 sm:px-3 sm:py-2 rounded-lg font-semibold transition-all duration-300 border border-yellow-500/30 hover:border-yellow-500/50 text-sm 2xl:text-base xl:text-sm lg:text-xs md:text-xs sm:text-xs">
                                        Baca Selengkapnya
                                    </button>
                                </div>
                            </div>
                        ))}
                    </div>
                </div>
            </section>


        </div>
    );
}

