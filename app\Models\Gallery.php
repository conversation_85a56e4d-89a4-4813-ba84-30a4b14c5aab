<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Gallery extends Model
{
    protected $table = 'gallery';

    protected $fillable = [
        'title',
        'description',
        'file_url',
        'file_type',
        'category',
        'zone',
        'views',
        'is_featured',
        'uploaded_by',
    ];

    protected function casts(): array
    {
        return [
            'is_featured' => 'boolean',
        ];
    }

    /**
     * Relationships
     */
    public function uploader(): BelongsTo
    {
        return $this->belongsTo(User::class, 'uploaded_by');
    }

    /**
     * Scopes
     */
    public function scopeByType($query, $type)
    {
        return $query->where('file_type', $type);
    }

    public function scopeByZone($query, $zone)
    {
        return $query->where('zone', $zone)->orWhere('zone', 'all');
    }

    public function scopeFeatured($query)
    {
        return $query->where('is_featured', true);
    }
}
