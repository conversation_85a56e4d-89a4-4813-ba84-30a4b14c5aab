import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Save, Image, Upload, Loader2 } from 'lucide-react';
import { toast } from 'react-hot-toast';
import api from '@/lib/axios';
import { z } from 'zod';

// Zod validation schema
const galleryFormSchema = z.object({
    title: z.string()
        .min(1, 'Title is required')
        .min(2, 'Title must be at least 2 characters')
        .max(100, 'Title must not exceed 100 characters'),
    subtitle: z.string()
        .min(1, 'Subtitle is required')
        .min(5, 'Subtitle must be at least 5 characters')
        .max(200, 'Subtitle must not exceed 200 characters'),
    description: z.string()
        .min(1, 'Description is required')
        .min(10, 'Description must be at least 10 characters')
        .max(500, 'Description must not exceed 500 characters'),
    background_image: z.string()
        .optional()
        .refine((val) => !val || val.startsWith('http') || val.startsWith('/'), {
            message: 'Background image must be a valid URL or file path'
        })
});

interface ContentItem {
    id: number;
    page: string;
    section: string;
    content_key: string;
    content_value: string;
    content_type: string;
    is_active: boolean;
    sort_order: number;
    created_at: string;
    updated_at: string;
}

interface GallerySectionEditorProps {
    content: ContentItem[];
    onUpdate?: () => void;
}

interface FormData {
    title: string;
    subtitle: string;
    description: string;
    background_image: string;
}

const GallerySectionEditor: React.FC<GallerySectionEditorProps> = ({ content, onUpdate }) => {
    const [formData, setFormData] = useState<FormData>({
        title: '',
        subtitle: '',
        description: '',
        background_image: ''
    });
    const [isLoading, setIsLoading] = useState(false);
    const [imageFile, setImageFile] = useState<File | null>(null);
    const [imagePreview, setImagePreview] = useState<string>('');
    const [isImageModalOpen, setIsImageModalOpen] = useState(false);
    const [isUploading, setIsUploading] = useState(false);
    const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});

    useEffect(() => {
        // Load existing content
        const galleryContent = content.filter(item => item.page === 'gallery' && item.section === 'hero');
        
        const newFormData = {
            title: galleryContent.find(item => item.content_key === 'title')?.content_value || '',
            subtitle: galleryContent.find(item => item.content_key === 'subtitle')?.content_value || '',
            description: galleryContent.find(item => item.content_key === 'description')?.content_value || '',
            background_image: galleryContent.find(item => item.content_key === 'background_image')?.content_value || ''
        };

        setFormData(newFormData);
    }, [content]);

    const handleInputChange = (key: keyof FormData, value: string) => {
        setFormData(prev => ({
            ...prev,
            [key]: value
        }));
    };

    // Function to compress image on client-side
    const compressImage = (file: File, maxWidth: number = 1920, maxHeight: number = 1080, quality: number = 0.8): Promise<File> => {
        return new Promise((resolve) => {
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            const img = new (window as any).Image();

            // Determine output format based on original file type
            const originalType = file.type;
            const isPng = originalType === 'image/png';
            const outputType = isPng ? 'image/png' : 'image/jpeg';

            img.onload = () => {
                // Calculate new dimensions while maintaining aspect ratio
                let { width, height } = img;

                if (width > height) {
                    if (width > maxWidth) {
                        height = (height * maxWidth) / width;
                        width = maxWidth;
                    }
                } else {
                    if (height > maxHeight) {
                        width = (width * maxHeight) / height;
                        height = maxHeight;
                    }
                }

                canvas.width = width;
                canvas.height = height;

                // For PNG with transparency, fill with transparent background
                if (isPng && ctx) {
                    ctx.clearRect(0, 0, width, height);
                }

                // Draw and compress
                ctx?.drawImage(img, 0, 0, width, height);

                canvas.toBlob((blob) => {
                    if (blob) {
                        const compressedFile = new File([blob], file.name, {
                            type: outputType,
                            lastModified: Date.now()
                        });
                        resolve(compressedFile);
                    } else {
                        resolve(file);
                    }
                }, outputType, isPng ? 1.0 : quality); // PNG uses lossless compression
            };

            img.src = URL.createObjectURL(file);
        });
    };

    const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
        const file = e.target.files?.[0];
        if (file) {
            try {
                // Show loading state
                toast.loading('Compressing image...');
                
                // Compress image before setting
                const compressedFile = await compressImage(file);
                
                // Show compression result
                const originalSizeMB = (file.size / 1024 / 1024).toFixed(2);
                const compressedSizeMB = (compressedFile.size / 1024 / 1024).toFixed(2);
                
                toast.dismiss();
                toast.success(`Image compressed: ${originalSizeMB}MB → ${compressedSizeMB}MB`);
                
                setImageFile(compressedFile);
                
                // Create preview
                const reader = new FileReader();
                reader.onload = (e) => {
                    const result = e.target?.result as string;
                    setImagePreview(result);
                };
                reader.readAsDataURL(compressedFile);
            } catch (error) {
                toast.dismiss();
                toast.error('Failed to compress image');
                console.error('Image compression error:', error);
            }
        }
    };



    const handleImageSave = async () => {
        setIsUploading(true);
        try {
            let finalImageUrl = '';

            if (imageFile) {
                const formData = new FormData();
                formData.append('image', imageFile);
                formData.append('section', 'gallery');
                formData.append('width', '1920');
                formData.append('height', '1080');
                formData.append('quality', '80');

                const response = await api.post('/cms/upload-image', formData, {
                    headers: { 'Content-Type': 'multipart/form-data' }
                });

                if (response.data.success) {
                    finalImageUrl = response.data.data.url;
                    toast.success(`Image uploaded! Size: ${response.data.data.file_size_kb}KB`);
                } else {
                    throw new Error(response.data.message);
                }
            }

            // Update form data and save to database
            if (finalImageUrl) {
                setFormData(prev => ({ ...prev, background_image: finalImageUrl }));

                // Save to database
                const galleryContent = content.filter(item => item.page === 'gallery' && item.section === 'hero');
                const backgroundImageItem = galleryContent.find(item => item.content_key === 'background_image');

                if (backgroundImageItem) {
                    await api.put(`/cms/page-content/${backgroundImageItem.id}`, {
                        content_value: finalImageUrl
                    });
                    toast.success('Background image saved to database!');
                    onUpdate(); // Refresh parent component
                }

                setIsImageModalOpen(false);
                setImageFile(null);
                setImagePreview('');
            }
        } catch (error: any) {
            toast.error(error.response?.data?.message || 'Failed to upload image');
        } finally {
            setIsUploading(false);
        }
    };

    const handleSave = async () => {
        setIsLoading(true);
        setValidationErrors({});

        try {
            // Validate form data using Zod
            const validationResult = galleryFormSchema.safeParse(formData);

            if (!validationResult.success) {
                const errors: Record<string, string> = {};
                validationResult.error.issues.forEach((issue) => {
                    if (issue.path[0]) {
                        errors[issue.path[0] as string] = issue.message;
                    }
                });
                setValidationErrors(errors);
                toast.error('Please fix the validation errors');
                return;
            }

            // Get gallery content
            const galleryContent = content.filter(item => item.page === 'gallery' && item.section === 'hero');

            const updates = [
                { key: 'title', value: formData.title },
                { key: 'subtitle', value: formData.subtitle },
                { key: 'description', value: formData.description }
            ];

            // Save text updates to database (background_image handled separately)
            for (const update of updates) {
                const existingItem = galleryContent.find(item => item.content_key === update.key);
                if (existingItem) {
                    await api.put(`/cms/page-content/${existingItem.id}`, {
                        content_value: update.value
                    });
                }
            }

            toast.success('Gallery content updated successfully!');
            onUpdate();
        } catch (error: any) {
            toast.error(error.response?.data?.message || 'Failed to save gallery content');
        } finally {
            setIsLoading(false);
        }
    };

    const handlePreview = () => {
        window.open('/gallery', '_blank');
    };

    return (
        <div className="space-y-6 p-6 bg-white rounded-lg border">
            <div className="flex items-center justify-between">
                <div>
                    <h3 className="text-lg font-semibold">Gallery Hero Section</h3>
                    <p className="text-sm text-muted-foreground">
                        Manage the hero section content for gallery page
                    </p>
                </div>
                <div className="flex gap-2">
                    <Button variant="outline" onClick={handlePreview}>
                        Preview Page
                    </Button>
                    <Button onClick={handleSave} disabled={isLoading}>
                        {isLoading ? (
                            <>
                                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                                Saving...
                            </>
                        ) : (
                            <>
                                <Save className="w-4 h-4 mr-2" />
                                Save Changes
                            </>
                        )}
                    </Button>
                </div>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* Form Fields */}
                <div className="space-y-4">
                    <div className="space-y-2">
                        <Label htmlFor="title">Title</Label>
                        <Input
                            id="title"
                            value={formData.title}
                            onChange={(e) => handleInputChange('title', e.target.value)}
                            placeholder="Enter gallery page title"
                            className={validationErrors.title ? 'border-red-500' : ''}
                        />
                        {validationErrors.title && (
                            <p className="text-sm text-red-500">{validationErrors.title}</p>
                        )}
                    </div>

                    <div className="space-y-2">
                        <Label htmlFor="subtitle">Subtitle</Label>
                        <Input
                            id="subtitle"
                            value={formData.subtitle}
                            onChange={(e) => handleInputChange('subtitle', e.target.value)}
                            placeholder="Enter gallery page subtitle"
                            className={validationErrors.subtitle ? 'border-red-500' : ''}
                        />
                        {validationErrors.subtitle && (
                            <p className="text-sm text-red-500">{validationErrors.subtitle}</p>
                        )}
                    </div>

                    <div className="space-y-2">
                        <Label htmlFor="description">Description</Label>
                        <Textarea
                            id="description"
                            value={formData.description}
                            onChange={(e) => handleInputChange('description', e.target.value)}
                            placeholder="Enter gallery page description"
                            rows={3}
                            className={validationErrors.description ? 'border-red-500' : ''}
                        />
                        {validationErrors.description && (
                            <p className="text-sm text-red-500">{validationErrors.description}</p>
                        )}
                    </div>
                </div>

                {/* Background Image */}
                <div className="space-y-4">
                    <div className="flex items-center justify-between">
                        <Label className="text-lg font-semibold">Background Image</Label>
                        <Dialog open={isImageModalOpen} onOpenChange={setIsImageModalOpen}>
                            <DialogTrigger asChild>
                                <Button variant="outline" className="flex items-center gap-2">
                                    <Image className="h-4 w-4" />
                                    Change Background
                                </Button>
                            </DialogTrigger>
                            <DialogContent className="max-w-2xl">
                                <DialogHeader>
                                    <DialogTitle>Change Background Image</DialogTitle>
                                    <DialogDescription>
                                        Upload an image file. Images will be automatically optimized and compressed.
                                    </DialogDescription>
                                </DialogHeader>

                                <div className="space-y-4">
                                    <div className="space-y-2">
                                        <Label htmlFor="image-file">Upload Image</Label>
                                        <Input
                                            id="image-file"
                                            type="file"
                                            accept="image/*"
                                            onChange={handleFileChange}
                                            className="cursor-pointer"
                                        />
                                        <p className="text-sm text-muted-foreground">
                                            Recommended: 1920x1080px or larger. Images will be compressed automatically.
                                        </p>
                                    </div>

                                    {imagePreview && (
                                        <div className="space-y-2">
                                            <Label>Preview</Label>
                                            <img
                                                src={imagePreview}
                                                alt="Preview"
                                                className="w-full h-48 object-cover rounded-lg border"
                                            />
                                        </div>
                                    )}
                                </div>

                                <div className="flex justify-end gap-2">
                                    <Button variant="outline" onClick={() => setIsImageModalOpen(false)}>
                                        Cancel
                                    </Button>
                                    <Button onClick={handleImageSave} disabled={!imageFile || isUploading}>
                                        {isUploading ? (
                                            <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                                        ) : (
                                            <Upload className="w-4 h-4 mr-2" />
                                        )}
                                        {isUploading ? 'Uploading...' : 'Save Image'}
                                    </Button>
                                </div>
                            </DialogContent>
                        </Dialog>
                    </div>

                    {/* Current Background Image */}
                    {formData.background_image && (
                        <div className="space-y-2">
                            <Label>Current Background</Label>
                            <img
                                src={formData.background_image}
                                alt="Current background"
                                className="w-full h-48 object-cover rounded-lg border"
                                onError={(e) => {
                                    e.currentTarget.src = '/placeholder-image.png';
                                }}
                            />
                        </div>
                    )}
                </div>
            </div>
        </div>
    );
};

export default GallerySectionEditor;
