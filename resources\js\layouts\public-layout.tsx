import { Outlet } from 'react-router-dom';
import { PublicHeader, PublicFooter } from '@/components/public';
import { SettingsProvider, useSettings } from '@/contexts/settings-context';
import DynamicFavicon from '@/components/DynamicFavicon';

function PublicLayoutContent() {
  const { settings, user } = useSettings();

  return (
    <div className="min-h-screen bg-white">
      {/* Dynamic Favicon */}
      <DynamicFavicon logoUrl={settings?.logo_url} />

      {/* Header */}
      <PublicHeader user={user} settings={settings} />

      {/* Main content */}
      <main>
        <Outlet />
      </main>

      {/* Footer */}
      <PublicFooter settings={settings} />
    </div>
  );
}

export default function PublicLayout() {
  return (
    <SettingsProvider>
      <PublicLayoutContent />
    </SettingsProvider>
  );
}
