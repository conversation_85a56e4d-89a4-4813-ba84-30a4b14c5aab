import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Save, Image, Link, Upload, Loader2, Plus, Edit, Trash2, RefreshCw } from 'lucide-react';
import { toast } from 'react-hot-toast';
import api from '@/lib/axios';
import { z } from 'zod';

interface ContentItem {
    id: number;
    page: string;
    section: string;
    content_key: string;
    content_value: string;
    content_type: string;
    is_active: boolean;
    sort_order: number;
}

interface SportsSectionEditorProps {
    content: ContentItem[];
    onUpdate: () => void;
}

interface SportsFormData {
    title: string;
    subtitle: string;
    background_image: string;
}

interface Sport {
    id: string;
    name: string;
    icon: string;
    description: string;
    participants: string;
    venue: string;
}

interface SportFormData {
    name: string;
    icon: string;
    description: string;
    participants: string;
    venue: string;
}

// Zod validation schema for sport form
const sportFormSchema = z.object({
    name: z.string()
        .min(1, 'Sport name is required')
        .min(2, 'Sport name must be at least 2 characters')
        .max(50, 'Sport name must not exceed 50 characters'),
    description: z.string()
        .min(1, 'Description is required')
        .min(10, 'Description must be at least 10 characters')
        .max(200, 'Description must not exceed 200 characters'),
    participants: z.string()
        .min(1, 'Participants information is required')
        .max(100, 'Participants information must not exceed 100 characters'),
    venue: z.string()
        .min(1, 'Venue is required')
        .max(100, 'Venue must not exceed 100 characters'),
    icon: z.string().optional() // Icon is optional since it can be uploaded as file
});

const SportsSectionEditor: React.FC<SportsSectionEditorProps> = ({ content, onUpdate }) => {
    const [formData, setFormData] = useState<SportsFormData>({
        title: '',
        subtitle: '',
        background_image: ''
    });
    
    const [isLoading, setIsLoading] = useState(false);
    const [isImageModalOpen, setIsImageModalOpen] = useState(false);
    const [imageFile, setImageFile] = useState<File | null>(null);
    const [imagePreview, setImagePreview] = useState<string>('');
    const [isUploading, setIsUploading] = useState(false);

    // Sports management state
    const [sports, setSports] = useState<Sport[]>([]);
    const [isSportModalOpen, setIsSportModalOpen] = useState(false);
    const [editingSport, setEditingSport] = useState<Sport | null>(null);
    const [sportFormData, setSportFormData] = useState<SportFormData>({
        name: '',
        icon: '',
        description: '',
        participants: '',
        venue: ''
    });

    // Sport icon upload state
    const [sportIconFile, setSportIconFile] = useState<File | null>(null);
    const [sportIconPreview, setSportIconPreview] = useState<string>('');

    // Validation errors state
    const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});

    // Delete confirmation state
    const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
    const [sportToDelete, setSportToDelete] = useState<Sport | null>(null);

    // Initialize form data
    useEffect(() => {
        // Get sports content
        const sportsContent = content.filter(item => item.page === 'homepage' && item.section === 'sports');

        const getContentValue = (key: string) => {
            const item = sportsContent.find(item => item.content_key === key);
            return item?.content_value || '';
        };

        setFormData({
            title: getContentValue('title'),
            subtitle: getContentValue('subtitle'),
            background_image: getContentValue('background_image')
        });

        setImagePreview(getContentValue('background_image'));

        // Load sports data
        const sportsData = getContentValue('sports_data');
        if (sportsData) {
            try {
                const parsedSports = JSON.parse(sportsData);
                setSports(parsedSports);
            } catch (error) {
                console.error('Error parsing sports data:', error);
                setSports([]);
            }
        } else {
            // Initialize with default sports if no data exists
            const defaultSports = [
                {
                    id: 'football',
                    name: 'Bola Sepak',
                    icon: '/images/bolasepakpsd.png',
                    description: 'Pertandingan bola sepak antara zon dengan format 11 vs 11 pemain.',
                    participants: '22 pemain per perlawanan',
                    venue: 'Padang Bola Sepak Utama'
                },
                {
                    id: 'netball',
                    name: 'Bola Jaring',
                    icon: '/images/netballpsd.png',
                    description: 'Sukan bola jaring yang dimainkan oleh dua pasukan dengan 7 pemain setiap satu.',
                    participants: '7 pemain per pasukan',
                    venue: 'Gelanggang Bola Jaring'
                }
            ];
            setSports(defaultSports);
        }
    }, [content]);

    const handleInputChange = (key: keyof SportsFormData, value: string) => {
        setFormData(prev => ({
            ...prev,
            [key]: value
        }));
    };

    const handleSave = async () => {
        setIsLoading(true);
        try {
            // Get sports content
            const sportsContent = content.filter(item => item.page === 'homepage' && item.section === 'sports');

            const updates = [
                { key: 'title', value: formData.title },
                { key: 'subtitle', value: formData.subtitle },
                { key: 'background_image', value: formData.background_image }
            ];

            for (const update of updates) {
                const existingItem = sportsContent.find(item => item.content_key === update.key);
                if (existingItem) {
                    await api.put(`/cms/page-content/${existingItem.id}`, {
                        content_value: update.value
                    });
                }
            }

            toast.success('Sports section updated successfully!');
            onUpdate();
        } catch (error) {
            toast.error('Failed to update sports section');
        } finally {
            setIsLoading(false);
        }
    };

    // Sync CMS sports data to database sports table
    const syncSportsToDatabase = async (sportsData: Sport[]) => {
        try {
            for (let i = 0; i < sportsData.length; i++) {
                const sport = sportsData[i];

                // Create or update sport in database
                await api.post('/admin/sports/sync', {
                    name: sport.name,
                    slug: sport.id,
                    description: sport.description,
                    logo_url: sport.icon,
                    participants: sport.participants,
                    venue: sport.venue,
                    display_order: i + 1,
                    is_active: true
                });
            }
            console.log('Sports synced to database successfully');
        } catch (error: any) {
            console.error('Error syncing sports to database:', error);
            // Don't throw error here, just log it
        }
    };

    // Sports management functions
    const saveSportsData = async (updatedSports: Sport[]) => {
        try {
            const sportsContent = content.filter(item => item.page === 'homepage' && item.section === 'sports');
            const sportsDataItem = sportsContent.find(item => item.content_key === 'sports_data');

            const sportsDataValue = JSON.stringify(updatedSports);

            if (sportsDataItem) {
                await api.put(`/cms/page-content/${sportsDataItem.id}`, {
                    content_value: sportsDataValue
                });
            } else {
                // Create new sports_data entry
                await api.post('/cms/page-content', {
                    page: 'homepage',
                    section: 'sports',
                    content_key: 'sports_data',
                    content_value: sportsDataValue,
                    content_type: 'json',
                    description: 'Sports carousel data'
                });
            }

            // Sync to database sports table
            await syncSportsToDatabase(updatedSports);

            setSports(updatedSports);
            onUpdate();
            toast.success('Sports data saved and synced to database successfully!');
        } catch (error: any) {
            console.error('Error saving sports data:', error);
            toast.error(error.response?.data?.message || 'Failed to save sports data');
            throw error;
        }
    };

    const handleAddSport = () => {
        setEditingSport(null);
        setSportFormData({
            name: '',
            icon: '',
            description: '',
            participants: '',
            venue: ''
        });
        setSportIconFile(null);
        setSportIconPreview('');
        setValidationErrors({});
        setIsSportModalOpen(true);
    };

    const handleEditSport = (sport: Sport) => {
        setEditingSport(sport);
        setSportFormData({
            name: sport.name,
            icon: sport.icon,
            description: sport.description,
            participants: sport.participants,
            venue: sport.venue
        });
        setSportIconFile(null);
        setSportIconPreview(sport.icon); // Show current icon as preview
        setValidationErrors({});
        setIsSportModalOpen(true);
    };

    const handleDeleteSport = (sport: Sport) => {
        setSportToDelete(sport);
        setIsDeleteModalOpen(true);
    };

    const confirmDeleteSport = async () => {
        if (!sportToDelete) return;

        try {
            const updatedSports = sports.filter(sport => sport.id !== sportToDelete.id);
            await saveSportsData(updatedSports);
            toast.success('Sport deleted successfully!');
            setIsDeleteModalOpen(false);
            setSportToDelete(null);
        } catch (error) {
            toast.error('Failed to delete sport');
        }
    };

    const handleSaveSport = async () => {
        try {
            setIsLoading(true);
            setValidationErrors({});

            // Validate form data using Zod
            const validationResult = sportFormSchema.safeParse(sportFormData);

            if (!validationResult.success) {
                const errors: Record<string, string> = {};
                validationResult.error.issues.forEach((issue) => {
                    if (issue.path[0]) {
                        errors[issue.path[0] as string] = issue.message;
                    }
                });
                setValidationErrors(errors);
                toast.error('Please fix the validation errors');
                return;
            }

            // Check if icon is required for new sport
            if (!editingSport && !sportIconFile && !sportFormData.icon) {
                setValidationErrors({ icon: 'Sport icon is required' });
                toast.error('Please upload a sport icon');
                return;
            }

            let iconUrl = sportFormData.icon;

            // Upload new icon if file is selected
            if (sportIconFile) {
                iconUrl = await uploadSportIcon(sportIconFile);
                toast.success('Icon uploaded successfully!');
            }

            const sportData = {
                ...sportFormData,
                icon: iconUrl,
                id: editingSport?.id || `sport_${Date.now()}`
            };

            let updatedSports;
            if (editingSport) {
                updatedSports = sports.map(sport =>
                    sport.id === editingSport.id ? sportData : sport
                );
            } else {
                updatedSports = [...sports, sportData];
            }

            await saveSportsData(updatedSports);

            toast.success(editingSport ? 'Sport updated successfully!' : 'Sport added successfully!');
            setIsSportModalOpen(false);
            setSportIconFile(null);
            setSportIconPreview('');
            setValidationErrors({});
        } catch (error) {
            toast.error('Failed to save sport');
        } finally {
            setIsLoading(false);
        }
    };

    // Background image handling functions
    // Function to compress image on client-side
    const compressImage = (file: File, maxWidth: number = 1920, maxHeight: number = 1080, quality: number = 0.8): Promise<File> => {
        return new Promise((resolve) => {
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            const img = new (window as any).Image();

            // Determine output format based on original file type
            const originalType = file.type;
            const isPng = originalType === 'image/png';
            const outputType = isPng ? 'image/png' : 'image/jpeg';

            img.onload = () => {
                // Calculate new dimensions while maintaining aspect ratio
                let { width, height } = img;

                if (width > height) {
                    if (width > maxWidth) {
                        height = (height * maxWidth) / width;
                        width = maxWidth;
                    }
                } else {
                    if (height > maxHeight) {
                        width = (width * maxHeight) / height;
                        height = maxHeight;
                    }
                }

                canvas.width = width;
                canvas.height = height;

                // For PNG with transparency, fill with transparent background
                if (isPng && ctx) {
                    ctx.clearRect(0, 0, width, height);
                }

                // Draw and compress
                ctx?.drawImage(img, 0, 0, width, height);

                canvas.toBlob((blob) => {
                    if (blob) {
                        const compressedFile = new File([blob], file.name, {
                            type: outputType,
                            lastModified: Date.now()
                        });
                        resolve(compressedFile);
                    } else {
                        resolve(file);
                    }
                }, outputType, isPng ? 1.0 : quality); // PNG uses lossless compression
            };

            img.src = URL.createObjectURL(file);
        });
    };

    const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
        const file = e.target.files?.[0];
        if (file) {
            try {
                // Show loading state
                toast.loading('Compressing image...');

                // Compress image before setting
                const compressedFile = await compressImage(file);

                // Show compression result
                const originalSizeMB = (file.size / 1024 / 1024).toFixed(2);
                const compressedSizeMB = (compressedFile.size / 1024 / 1024).toFixed(2);

                toast.dismiss();
                toast.success(`Image compressed: ${originalSizeMB}MB → ${compressedSizeMB}MB`);

                setImageFile(compressedFile);

                // Create preview
                const reader = new FileReader();
                reader.onload = (e) => {
                    const result = e.target?.result as string;
                    setImagePreview(result);
                };
                reader.readAsDataURL(compressedFile);
            } catch (error) {
                toast.dismiss();
                toast.error('Failed to compress image');
                console.error('Image compression error:', error);
            }
        }
    };

    const handleImageSave = async () => {
        setIsUploading(true);
        try {
            let finalImageUrl = '';

            if (imageFile) {
                // Upload and process file
                const formData = new FormData();
                formData.append('image', imageFile);
                formData.append('section', 'sports');
                formData.append('width', '1920');
                formData.append('height', '1080');
                formData.append('quality', '80');

                const response = await api.post('/cms/upload-image', formData, {
                    headers: { 'Content-Type': 'multipart/form-data' }
                });

                if (response.data.success) {
                    finalImageUrl = response.data.data.url;
                    toast.success(`Image uploaded! Size: ${response.data.data.file_size_kb}KB`);
                } else {
                    throw new Error(response.data.message);
                }
            }

            // Update form data and save to database
            if (finalImageUrl) {
                setFormData(prev => ({ ...prev, background_image: finalImageUrl }));

                // Save to database
                const sportsContent = content.filter(item => item.page === 'homepage' && item.section === 'sports');
                const backgroundImageItem = sportsContent.find(item => item.content_key === 'background_image');

                if (backgroundImageItem) {
                    await api.put(`/cms/page-content/${backgroundImageItem.id}`, {
                        content_value: finalImageUrl
                    });
                    toast.success('Background image saved to database!');
                    onUpdate(); // Refresh parent component
                }

                setIsImageModalOpen(false);
                setImageFile(null);
            }
        } catch (error: any) {
            toast.error(error.response?.data?.message || 'Failed to process image');
        } finally {
            setIsUploading(false);
        }
    };

    // Sport icon handling functions
    const handleSportIconChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const file = e.target.files?.[0];
        if (file) {
            setSportIconFile(file);
            const reader = new FileReader();
            reader.onload = (e) => {
                const result = e.target?.result as string;
                setSportIconPreview(result);
            };
            reader.readAsDataURL(file);
        }
    };

    const uploadSportIcon = async (file: File): Promise<string> => {
        try {
            // Compress icon image (smaller size for icons)
            const compressedFile = await compressImage(file, 200, 200, 0.9);

            const formData = new FormData();
            formData.append('image', compressedFile);
            formData.append('section', 'sports-icons');
            formData.append('width', '200');
            formData.append('height', '200');
            formData.append('quality', '90');

            const response = await api.post('/cms/upload-image', formData, {
                headers: { 'Content-Type': 'multipart/form-data' }
            });

            if (response.data.success) {
                return response.data.data.url;
            } else {
                throw new Error(response.data.message);
            }
        } catch (error) {
            console.error('Sport icon upload error:', error);
            throw error;
        }
    };

    return (
        <>
            <Card>
                <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                        Sports Categories Section
                        <Badge variant="outline">{sports.length} sports</Badge>
                    </CardTitle>
                    <CardDescription>
                        Edit sports categories section content including title, subtitle, background image, and manage sports carousel items
                    </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                    {/* Text Fields */}
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div className="space-y-2">
                            <Label htmlFor="sports-title">Title</Label>
                            <Input
                                id="sports-title"
                                value={formData.title}
                                onChange={(e) => handleInputChange('title', e.target.value)}
                                placeholder="KATEGORI SUKAN"
                            />
                        </div>
                        
                        <div className="space-y-2">
                            <Label htmlFor="sports-subtitle">Subtitle</Label>
                            <Textarea
                                id="sports-subtitle"
                                value={formData.subtitle}
                                onChange={(e) => handleInputChange('subtitle', e.target.value)}
                                placeholder="7 kategori sukan yang akan dipertandingkan..."
                                rows={2}
                            />
                        </div>
                    </div>

                    {/* Background Image */}
                    <div className="space-y-4">
                        <div className="flex items-center justify-between">
                            <Label className="text-lg font-semibold">Background Image</Label>
                            <Dialog open={isImageModalOpen} onOpenChange={setIsImageModalOpen}>
                                <DialogTrigger asChild>
                                    <Button variant="outline" className="flex items-center gap-2">
                                        <Image className="h-4 w-4" />
                                        Change Background
                                    </Button>
                                </DialogTrigger>
                                <DialogContent className="max-w-2xl">
                                    <DialogHeader>
                                        <DialogTitle>Change Background Image</DialogTitle>
                                        <DialogDescription>
                                            Upload an image file. Images will be automatically optimized and compressed.
                                        </DialogDescription>
                                    </DialogHeader>

                                    <div className="space-y-4">
                                        <div className="space-y-2">
                                            <Label htmlFor="image-file">Upload Image</Label>
                                            <Input
                                                id="image-file"
                                                type="file"
                                                accept="image/*"
                                                onChange={handleFileChange}
                                                className="cursor-pointer"
                                            />
                                        </div>

                                        {imagePreview && (
                                            <div className="space-y-2">
                                                <Label>Preview</Label>
                                                <div className="border rounded-lg p-2">
                                                    <img
                                                        src={imagePreview}
                                                        alt="Preview"
                                                        className="w-full h-48 object-cover rounded"
                                                    />
                                                </div>
                                            </div>
                                        )}
                                    </div>

                                    <div className="flex justify-end gap-2 pt-4">
                                        <Button variant="outline" onClick={() => setIsImageModalOpen(false)}>
                                            Cancel
                                        </Button>
                                        <Button onClick={handleImageSave} disabled={!imageFile || isUploading}>
                                            {isUploading ? (
                                                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                                            ) : (
                                                <Upload className="h-4 w-4 mr-2" />
                                            )}
                                            {isUploading ? 'Uploading...' : 'Upload & Save'}
                                        </Button>
                                    </div>
                                </DialogContent>
                            </Dialog>
                        </div>

                        {formData.background_image && (
                            <div className="border rounded-lg p-2">
                                <img
                                    src={formData.background_image}
                                    alt="Current background"
                                    className="w-full h-32 object-cover rounded"
                                />
                                <p className="text-sm text-gray-500 mt-2">Current background image</p>
                            </div>
                        )}
                    </div>

                    {/* Sports Management */}
                    <div className="space-y-4">
                        <div className="flex items-center justify-between">
                            <Label className="text-lg font-semibold">Sports Carousel Items</Label>
                            <div className="flex items-center gap-2">
                                <Button
                                    onClick={() => syncSportsToDatabase(sports)}
                                    variant="outline"
                                    className="flex items-center gap-2"
                                    title="Sync sports to database for admin players page"
                                >
                                    <RefreshCw className="h-4 w-4" />
                                    Sync to Database
                                </Button>
                                <Button onClick={handleAddSport} className="flex items-center gap-2">
                                    <Plus className="h-4 w-4" />
                                    Add Sport
                                </Button>
                            </div>
                        </div>
                        
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-2 xl:grid-cols-3 gap-4">
                            {sports.map((sport) => (
                                <Card key={sport.id} className="relative">
                                    <CardContent className="p-4">
                                        <div className="flex items-start gap-3">
                                            {sport.icon && (
                                                <img
                                                    src={sport.icon}
                                                    alt={sport.name}
                                                    className="w-12 h-12 object-cover rounded"
                                                />
                                            )}
                                            <div className="flex-1 min-w-0">
                                                <h4 className="font-semibold text-sm truncate">{sport.name}</h4>
                                                <p className="text-xs text-gray-600 line-clamp-2">{sport.description}</p>
                                                <p className="text-xs text-gray-500 mt-1">{sport.participants}</p>
                                            </div>
                                        </div>
                                        
                                        <div className="flex justify-end gap-1 mt-3">
                                            <Button
                                                size="sm"
                                                variant="outline"
                                                onClick={() => handleEditSport(sport)}
                                                className="h-8 w-8 p-0"
                                            >
                                                <Edit className="h-3 w-3" />
                                            </Button>
                                            <Button
                                                size="sm"
                                                variant="outline"
                                                onClick={() => handleDeleteSport(sport)}
                                                className="h-8 w-8 p-0 text-red-600 hover:text-red-700"
                                            >
                                                <Trash2 className="h-3 w-3" />
                                            </Button>
                                        </div>
                                    </CardContent>
                                </Card>
                            ))}
                        </div>
                        
                        {sports.length === 0 && (
                            <div className="text-center py-8 text-gray-500">
                                <p>No sports added yet. Click "Add Sport" to get started.</p>
                            </div>
                        )}
                    </div>

                    {/* Save Button */}
                    <div className="flex justify-end pt-4">
                        <Button onClick={handleSave} disabled={isLoading} className="flex items-center gap-2">
                            {isLoading ? (
                                <Loader2 className="h-4 w-4 animate-spin" />
                            ) : (
                                <Save className="h-4 w-4" />
                            )}
                            {isLoading ? 'Saving...' : 'Save Changes'}
                        </Button>
                    </div>
                </CardContent>
            </Card>

            {/* Sport Add/Edit Modal */}
            <Dialog open={isSportModalOpen} onOpenChange={setIsSportModalOpen}>
                <DialogContent className="max-w-2xl">
                    <DialogHeader>
                        <DialogTitle>
                            {editingSport ? 'Edit Sport' : 'Add New Sport'}
                        </DialogTitle>
                        <DialogDescription>
                            {editingSport ? 'Update sport information' : 'Add a new sport to the carousel'}
                        </DialogDescription>
                    </DialogHeader>
                    
                    <div className="space-y-4">
                        {/* Sport Name */}
                        <div className="space-y-2">
                            <Label htmlFor="sport-name">Sport Name</Label>
                            <Input
                                id="sport-name"
                                value={sportFormData.name}
                                onChange={(e) => setSportFormData(prev => ({ ...prev, name: e.target.value }))}
                                placeholder="e.g., Bola Sepak"
                                className={validationErrors.name ? 'border-red-500' : ''}
                            />
                            {validationErrors.name && (
                                <p className="text-sm text-red-500">{validationErrors.name}</p>
                            )}
                        </div>
                        
                        {/* Sport Description */}
                        <div className="space-y-2">
                            <Label htmlFor="sport-description">Description</Label>
                            <Textarea
                                id="sport-description"
                                value={sportFormData.description}
                                onChange={(e) => setSportFormData(prev => ({ ...prev, description: e.target.value }))}
                                placeholder="Brief description of the sport..."
                                rows={3}
                                className={validationErrors.description ? 'border-red-500' : ''}
                            />
                            {validationErrors.description && (
                                <p className="text-sm text-red-500">{validationErrors.description}</p>
                            )}
                        </div>
                        
                        {/* Participants */}
                        <div className="space-y-2">
                            <Label htmlFor="sport-participants">Participants</Label>
                            <Input
                                id="sport-participants"
                                value={sportFormData.participants}
                                onChange={(e) => setSportFormData(prev => ({ ...prev, participants: e.target.value }))}
                                placeholder="e.g., 22 pemain per perlawanan"
                                className={validationErrors.participants ? 'border-red-500' : ''}
                            />
                            {validationErrors.participants && (
                                <p className="text-sm text-red-500">{validationErrors.participants}</p>
                            )}
                        </div>
                        
                        {/* Venue */}
                        <div className="space-y-2">
                            <Label htmlFor="sport-venue">Venue</Label>
                            <Input
                                id="sport-venue"
                                value={sportFormData.venue}
                                onChange={(e) => setSportFormData(prev => ({ ...prev, venue: e.target.value }))}
                                placeholder="e.g., Padang Bola Sepak Utama"
                                className={validationErrors.venue ? 'border-red-500' : ''}
                            />
                            {validationErrors.venue && (
                                <p className="text-sm text-red-500">{validationErrors.venue}</p>
                            )}
                        </div>
                        
                        {/* Sport Icon Upload */}
                        <div className="space-y-2">
                            <Label htmlFor="sport-icon-file">Sport Icon</Label>
                            <Input
                                id="sport-icon-file"
                                type="file"
                                accept="image/*"
                                onChange={handleSportIconChange}
                                className={`cursor-pointer ${validationErrors.icon ? 'border-red-500' : ''}`}
                            />
                            {validationErrors.icon && (
                                <p className="text-sm text-red-500">{validationErrors.icon}</p>
                            )}
                            {sportIconPreview && (
                                <div className="mt-2">
                                    <img
                                        src={sportIconPreview}
                                        alt="Icon preview"
                                        className="w-16 h-16 object-cover rounded border"
                                    />
                                </div>
                            )}
                        </div>
                    </div>
                    
                    <div className="flex justify-end gap-2 pt-4">
                        <Button variant="outline" onClick={() => setIsSportModalOpen(false)}>
                            Cancel
                        </Button>
                        <Button onClick={handleSaveSport} disabled={isLoading}>
                            {isLoading ? (
                                <>
                                    <Loader2 className="h-4 w-4 animate-spin mr-2" />
                                    Saving...
                                </>
                            ) : (
                                <>
                                    <Save className="h-4 w-4 mr-2" />
                                    {editingSport ? 'Update Sport' : 'Add Sport'}
                                </>
                            )}
                        </Button>
                    </div>
                </DialogContent>
            </Dialog>

            {/* Delete Confirmation Modal */}
            <Dialog open={isDeleteModalOpen} onOpenChange={setIsDeleteModalOpen}>
                <DialogContent className="max-w-md">
                    <DialogHeader>
                        <DialogTitle>Confirm Delete</DialogTitle>
                        <DialogDescription>
                            Are you sure you want to delete the sport "{sportToDelete?.name}"? This action cannot be undone.
                        </DialogDescription>
                    </DialogHeader>

                    <div className="flex justify-end gap-2 pt-4">
                        <Button
                            variant="outline"
                            onClick={() => setIsDeleteModalOpen(false)}
                        >
                            Cancel
                        </Button>
                        <Button
                            variant="destructive"
                            onClick={confirmDeleteSport}
                            disabled={isLoading}
                        >
                            {isLoading ? (
                                <>
                                    <Loader2 className="h-4 w-4 animate-spin mr-2" />
                                    Deleting...
                                </>
                            ) : (
                                <>
                                    <Trash2 className="h-4 w-4 mr-2" />
                                    Delete Sport
                                </>
                            )}
                        </Button>
                    </div>
                </DialogContent>
            </Dialog>
        </>
    );
};

export default SportsSectionEditor;
