import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Save, Upload, Eye, Loader2 } from 'lucide-react';
import api from '@/lib/axios';
import toast from 'react-hot-toast';

interface ContentItem {
    id: number;
    page: string;
    section: string;
    content_key: string;
    content_value: string;
    content_type: string;
    is_active: boolean;
    sort_order: number;
}

interface MatchScheduleSectionEditorProps {
    content: ContentItem[];
    onUpdate: () => void;
}

interface MatchScheduleFormData {
    title: string;
    subtitle: string;
    description: string;
    background_image: string;
}

const MatchScheduleSectionEditor: React.FC<MatchScheduleSectionEditorProps> = ({ content, onUpdate }) => {
    const [formData, setFormData] = useState<MatchScheduleFormData>({
        title: '',
        subtitle: '',
        description: '',
        background_image: ''
    });
    const [isLoading, setIsLoading] = useState(false);
    const [imageFile, setImageFile] = useState<File | null>(null);
    const [imagePreview, setImagePreview] = useState<string>('');
    const [isImageModalOpen, setIsImageModalOpen] = useState(false);
    const [isUploading, setIsUploading] = useState(false);

    useEffect(() => {
        // Load existing content
        const matchScheduleContent = content.filter(item => item.page === 'match-schedule' && item.section === 'hero');

        const newFormData = {
            title: matchScheduleContent.find(item => item.content_key === 'title')?.content_value || '',
            subtitle: matchScheduleContent.find(item => item.content_key === 'subtitle')?.content_value || '',
            description: matchScheduleContent.find(item => item.content_key === 'description')?.content_value || '',
            background_image: matchScheduleContent.find(item => item.content_key === 'background_image')?.content_value || ''
        };

        setFormData(newFormData);
    }, [content]);

    const handleInputChange = (key: keyof MatchScheduleFormData, value: string) => {
        setFormData(prev => ({
            ...prev,
            [key]: value
        }));
    };

    // Function to compress image on client-side
    const compressImage = (file: File, maxWidth: number = 1920, maxHeight: number = 1080, quality: number = 0.8): Promise<File> => {
        return new Promise((resolve) => {
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            const img = new (window as any).Image();

            // Determine output format based on original file type
            const originalType = file.type;
            const isPng = originalType === 'image/png';
            const outputType = isPng ? 'image/png' : 'image/jpeg';

            img.onload = () => {
                // Calculate new dimensions while maintaining aspect ratio
                let { width, height } = img;

                if (width > height) {
                    if (width > maxWidth) {
                        height = (height * maxWidth) / width;
                        width = maxWidth;
                    }
                } else {
                    if (height > maxHeight) {
                        width = (width * maxHeight) / height;
                        height = maxHeight;
                    }
                }

                canvas.width = width;
                canvas.height = height;

                // For PNG with transparency, fill with transparent background
                if (isPng && ctx) {
                    ctx.clearRect(0, 0, width, height);
                }

                // Draw and compress
                ctx?.drawImage(img, 0, 0, width, height);

                canvas.toBlob((blob) => {
                    if (blob) {
                        const compressedFile = new File([blob], file.name, {
                            type: outputType,
                            lastModified: Date.now()
                        });
                        resolve(compressedFile);
                    } else {
                        resolve(file);
                    }
                }, outputType, isPng ? 1.0 : quality); // PNG uses lossless compression
            };

            img.src = URL.createObjectURL(file);
        });
    };

    const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
        const file = e.target.files?.[0];
        if (file) {
            try {
                // Show loading state
                toast.loading('Compressing image...');

                // Compress image before setting
                const compressedFile = await compressImage(file);

                // Show compression result
                const originalSizeMB = (file.size / 1024 / 1024).toFixed(2);
                const compressedSizeMB = (compressedFile.size / 1024 / 1024).toFixed(2);

                toast.dismiss();
                toast.success(`Image compressed: ${originalSizeMB}MB → ${compressedSizeMB}MB`);

                setImageFile(compressedFile);

                // Create preview
                const reader = new FileReader();
                reader.onload = (e) => {
                    const result = e.target?.result as string;
                    setImagePreview(result);
                };
                reader.readAsDataURL(compressedFile);
            } catch (error) {
                toast.dismiss();
                toast.error('Failed to compress image');
                console.error('Image compression error:', error);
            }
        }
    };

    const handleImageSave = async () => {
        setIsUploading(true);
        try {
            let finalImageUrl = '';

            if (imageFile) {
                const formData = new FormData();
                formData.append('image', imageFile);
                formData.append('section', 'match-schedule');
                formData.append('width', '1920');
                formData.append('height', '1080');
                formData.append('quality', '80');

                const response = await api.post('/cms/upload-image', formData, {
                    headers: { 'Content-Type': 'multipart/form-data' }
                });

                if (response.data.success) {
                    finalImageUrl = response.data.data.url;
                    toast.success(`Image uploaded! Size: ${response.data.data.file_size_kb}KB`);
                } else {
                    throw new Error(response.data.message);
                }
            }

            // Update form data and save to database
            if (finalImageUrl) {
                setFormData(prev => ({ ...prev, background_image: finalImageUrl }));

                // Save to database
                const matchScheduleContent = content.filter(item => item.page === 'match-schedule' && item.section === 'hero');
                const backgroundImageItem = matchScheduleContent.find(item => item.content_key === 'background_image');

                if (backgroundImageItem) {
                    await api.put(`/cms/page-content/${backgroundImageItem.id}`, {
                        content_value: finalImageUrl
                    });
                    toast.success('Background image saved to database!');
                    onUpdate(); // Refresh parent component
                }

                setIsImageModalOpen(false);
                setImageFile(null);
            }
        } catch (error: any) {
            toast.error(error.response?.data?.message || 'Failed to upload image');
        } finally {
            setIsUploading(false);
        }
    };

    const handleSave = async () => {
        setIsLoading(true);
        try {
            // Get match schedule content
            const matchScheduleContent = content.filter(item => item.page === 'match-schedule' && item.section === 'hero');

            const updates = [
                { key: 'title', value: formData.title },
                { key: 'subtitle', value: formData.subtitle },
                { key: 'description', value: formData.description }
            ];

            // Save text updates to database (background_image handled separately)
            for (const update of updates) {
                const existingItem = matchScheduleContent.find(item => item.content_key === update.key);
                if (existingItem) {
                    await api.put(`/cms/page-content/${existingItem.id}`, {
                        content_value: update.value
                    });
                }
            }

            toast.success('Kandungan berjaya dikemaskini');
            onUpdate();

        } catch (error) {
            console.error('Save failed:', error);
            toast.error('Gagal menyimpan kandungan');
        } finally {
            setIsLoading(false);
        }
    };

    const handlePreview = () => {
        window.open('/match-schedule', '_blank');
    };

    return (
        <Card>
            <CardHeader>
                <CardTitle className="flex items-center gap-2">
                    Match Schedule Page - Hero Section
                    <Badge variant="outline">4 fields</Badge>
                </CardTitle>
                <CardDescription>
                    Edit match schedule page hero section content including title, subtitle, description, and background image
                </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
                {/* Text Fields */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                        <Label htmlFor="title">Title</Label>
                        <Input
                            id="title"
                            value={formData.title}
                            onChange={(e) => handleInputChange('title', e.target.value)}
                            placeholder="JADUAL PERLAWANAN"
                        />
                    </div>

                    <div className="space-y-2">
                        <Label htmlFor="description">Date & Venue</Label>
                        <Input
                            id="description"
                            value={formData.description}
                            onChange={(e) => handleInputChange('description', e.target.value)}
                            placeholder="21 - 25 Julai 2025 | PU Sendayan"
                        />
                    </div>
                </div>

                <div className="space-y-2">
                    <Label htmlFor="subtitle">Subtitle</Label>
                    <Textarea
                        id="subtitle"
                        value={formData.subtitle}
                        onChange={(e) => handleInputChange('subtitle', e.target.value)}
                        placeholder="Jadual lengkap semua perlawanan Sukan Intra Kor Kesihatan DiRaja 2025"
                        rows={3}
                    />
                </div>

                {/* Background Image Section */}
                <div className="space-y-4">
                    <Label>Background Image</Label>
                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                        {/* Image Preview */}
                        <div className="space-y-2">
                            <Label className="text-sm text-gray-600">Current Image</Label>
                            <div className="relative aspect-video bg-gray-100 rounded-lg overflow-hidden border-2 border-dashed border-gray-300">
                                {formData.background_image ? (
                                    <img
                                        src={formData.background_image}
                                        alt="Background preview"
                                        className="w-full h-full object-cover"
                                    />
                                ) : (
                                    <div className="flex items-center justify-center h-full text-gray-400">
                                        No image selected
                                    </div>
                                )}
                            </div>
                        </div>

                        {/* Upload Controls */}
                        <div className="space-y-4">
                            <div className="space-y-2">
                                <Label className="text-sm text-gray-600">Upload New Image</Label>
                                <Dialog open={isImageModalOpen} onOpenChange={setIsImageModalOpen}>
                                    <DialogTrigger asChild>
                                        <Button variant="outline" className="w-full">
                                            <Upload className="w-4 h-4 mr-2" />
                                            Change Background Image
                                        </Button>
                                    </DialogTrigger>
                                    <DialogContent className="sm:max-w-md">
                                        <DialogHeader>
                                            <DialogTitle>Upload Background Image</DialogTitle>
                                        </DialogHeader>

                                        <div className="space-y-4">
                                            <div className="space-y-2">
                                                <Label htmlFor="image-file">Upload Image</Label>
                                                <Input
                                                    id="image-file"
                                                    type="file"
                                                    accept="image/*"
                                                    onChange={handleFileChange}
                                                    className="cursor-pointer"
                                                />
                                                <p className="text-sm text-muted-foreground">
                                                    Recommended: 1920x1080px or larger. Images will be compressed automatically.
                                                </p>
                                            </div>

                                            {imagePreview && (
                                                <div className="space-y-2">
                                                    <Label>Preview</Label>
                                                    <div className="border rounded-lg p-2">
                                                        <img
                                                            src={imagePreview}
                                                            alt="Preview"
                                                            className="w-full h-48 object-cover rounded"
                                                        />
                                                    </div>
                                                </div>
                                            )}
                                        </div>

                                        <div className="flex justify-end gap-2 pt-4">
                                            <Button variant="outline" onClick={() => setIsImageModalOpen(false)}>
                                                Cancel
                                            </Button>
                                            <Button onClick={handleImageSave} disabled={!imageFile || isUploading}>
                                                {isUploading ? (
                                                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                                                ) : (
                                                    <Upload className="h-4 w-4 mr-2" />
                                                )}
                                                {isUploading ? 'Uploading...' : 'Upload & Save'}
                                            </Button>
                                        </div>
                                    </DialogContent>
                                </Dialog>
                            </div>
                        </div>
                    </div>
                </div>

                {/* Action Buttons */}
                <div className="flex flex-wrap gap-3 pt-4 border-t justify-end">
                    <Button
                        variant="outline"
                        onClick={handlePreview}
                        className="flex items-center gap-2"
                    >
                        <Eye className="w-4 h-4" />
                        Preview Page
                    </Button>

                    <Button
                        onClick={handleSave}
                        disabled={isLoading}
                        className="flex items-center gap-2"
                    >
                        {isLoading ? (
                            <Loader2 className="w-4 h-4 animate-spin" />
                        ) : (
                            <Save className="w-4 h-4" />
                        )}
                        {isLoading ? 'Saving...' : 'Save Changes'}
                    </Button>
                </div>
            </CardContent>
        </Card>
    );
};

export default MatchScheduleSectionEditor;
