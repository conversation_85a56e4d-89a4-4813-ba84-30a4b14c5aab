import { NavMain } from '@/components/nav-main';
import { NavUser } from '@/components/nav-user';
import { Sidebar, SidebarContent, SidebarFooter, SidebarHeader, SidebarMenu, SidebarMenuButton, SidebarMenuItem } from '@/components/ui/sidebar';
import { type NavItem } from '@/types';
import { Link } from 'react-router-dom';
import { useAuth } from '@/contexts/auth-context';
import { useZone } from '@/contexts/zone-context';
import { useEffect } from 'react';
import {
    LayoutGrid,
    Users,
    Trophy,
    FileText,
    Settings,
    BarChart3,
    Building2,
    Globe,
    Camera,
    Newspaper,
    ExternalLink
} from 'lucide-react';
import AppLogo from './app-logo';

export function AppSidebar() {
    const { user } = useAuth();
    const { zone, loading } = useZone();

    // Debug: Log user data
    console.log('AppSidebar - User:', user);

    // Safety check: Return early if user is not loaded
    if (!user) {
        return (
            <Sidebar collapsible="icon" variant="inset">
                <SidebarHeader>
                    <SidebarMenu>
                        <SidebarMenuItem>
                            <SidebarMenuButton size="lg" asChild>
                                <AppLogo />
                            </SidebarMenuButton>
                        </SidebarMenuItem>
                    </SidebarMenu>
                </SidebarHeader>
                <SidebarContent>
                    <div className="p-4 text-center text-muted-foreground">
                        Loading...
                    </div>
                </SidebarContent>
            </Sidebar>
        );
    }



    // Role-based navigation items
    const getMainNavItems = (): NavItem[] => {
        const baseItems: NavItem[] = [
            {
                title: 'Dashboard',
                href: '/dashboard',
                icon: LayoutGrid,
            },
        ];

        return baseItems;
    };

    // Public pages navigation items (available to all roles)
    const getPublicNavItems = (): NavItem[] => {
        return [
            {
                title: 'Portal Utama',
                href: '/',
                icon: Globe,
            },
            {
                title: 'Galeri',
                href: '/gallery',
                icon: Camera,
            },
            {
                title: 'Berita',
                href: '/news',
                icon: Newspaper,
            },
        ];
    };

    // Admin-specific navigation items
    const getAdminNavItems = (): NavItem[] => {
        const adminItems: NavItem[] = [];

        // Debug: Log user role
        console.log('getAdminNavItems - User role:', user?.role);

        // Add role-specific items
        if (user?.role === 'superadmin') {
            adminItems.push(
                {
                    title: 'Pengurusan Peserta',
                    href: '/dashboard/admin/players',
                    icon: Users,
                },
                {
                    title: 'Pengurusan Perlawanan',
                    href: '/dashboard/admin/matches',
                    icon: Trophy,
                },
                {
                    title: 'CMS - Page Content',
                    href: '/dashboard/admin/cms/page-content',
                    icon: FileText,
                },
                {
                    title: 'Laporan & Statistik',
                    href: '/dashboard/admin/reports',
                    icon: BarChart3,
                },
                {
                    title: 'Tetapan Sistem',
                    href: '/dashboard/admin/settings',
                    icon: Settings,
                }
            );
        } else if (user.role === 'admin') {
            adminItems.push(
                {
                    title: 'Pengurusan Peserta',
                    href: '/dashboard/admin/players',
                    icon: Users,
                },
                {
                    title: 'Pengurusan Perlawanan',
                    href: '/dashboard/admin/matches',
                    icon: Trophy,
                },
                {
                    title: 'Laporan Zon',
                    href: '/dashboard/admin/reports',
                    icon: BarChart3,
                }
            );
        } else if (user.role === 'zone') {
            const zoneName = zone?.name || `Zon ${user.zone}`;
            adminItems.push(
                {
                    title: `Profil ${zoneName}`,
                    href: '/dashboard/admin/zone-profile',
                    icon: Building2,
                },
                {
                    title: `Peserta ${zoneName}`,
                    href: '/dashboard/admin/players',
                    icon: Users,
                },
                {
                    title: 'Jadual Perlawanan',
                    href: '/dashboard/admin/jadual-pertandingan',
                    icon: Trophy,
                }
            );
        } else if (user.role === 'media') {
            adminItems.push(
                {
                    title: 'CMS - Page Content',
                    href: '/dashboard/admin/cms/page-content',
                    icon: FileText,
                },
            );
        }

        return adminItems;
    };

    return (
        <Sidebar collapsible="icon" variant="inset">
            <SidebarHeader>
                <SidebarMenu>
                    <SidebarMenuItem>
                        <SidebarMenuButton size="lg" asChild>
                            <Link to="/dashboard">
                                {user.role === 'zone' && zone && !loading ? (
                                    <div className="flex items-center gap-2">
                                        {zone.logo_url ? (
                                            <img
                                                src={zone.logo_url}
                                                alt={`Logo ${zone.name}`}
                                                className="h-8 w-8 object-contain"
                                            />
                                        ) : (
                                            <Building2 className="h-8 w-8" />
                                        )}
                                        <div className="flex flex-col">
                                            <span className="text-sm font-semibold">{zone.name}</span>
                                            <span className="text-xs text-muted-foreground">Portal Sukan</span>
                                        </div>
                                    </div>
                                ) : (
                                    <AppLogo />
                                )}
                            </Link>
                        </SidebarMenuButton>
                    </SidebarMenuItem>
                </SidebarMenu>
            </SidebarHeader>

            <SidebarContent>
                <NavMain items={getMainNavItems()} label="Dashboard" />
                <NavMain items={getAdminNavItems()} label="Pengurusan" />
                <NavMain items={getPublicNavItems()} label="Portal Awam" />
            </SidebarContent>

            <SidebarFooter>
                <NavUser />
            </SidebarFooter>
        </Sidebar>
    );
}
