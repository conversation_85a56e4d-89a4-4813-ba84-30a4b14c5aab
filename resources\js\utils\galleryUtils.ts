import toast from 'react-hot-toast';

interface MediaItem {
    id: number;
    title: string;
    description: string;
    file_url: string;
    file_urls?: string[];
    image_count?: number;
    file_type: 'image' | 'video';
    category: string;
    zone: string;
    views: number;
    uploaded_at: string;
    uploader?: string;
}

/**
 * Download image from URL
 */
export const downloadImage = async (url: string, filename: string) => {
    try {
        toast.loading('Preparing download...');
        
        // Fetch the image
        const response = await fetch(url);
        if (!response.ok) {
            throw new Error('Failed to fetch image');
        }
        
        const blob = await response.blob();
        
        // Create download link
        const downloadUrl = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = downloadUrl;
        link.download = filename;
        
        // Trigger download
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        
        // Clean up
        window.URL.revokeObjectURL(downloadUrl);
        
        toast.dismiss();
        toast.success('Download started!');
    } catch (error) {
        console.error('Download error:', error);
        toast.dismiss();
        toast.error('Failed to download image');
    }
};

/**
 * Download all images from a media item
 */
export const downloadAllImages = async (media: MediaItem) => {
    const images = media.file_urls && media.file_urls.length > 0 
        ? media.file_urls 
        : [media.file_url];
    
    if (images.length === 1) {
        const filename = `${media.title.replace(/[^a-z0-9]/gi, '_').toLowerCase()}.jpg`;
        await downloadImage(images[0], filename);
    } else {
        // Download multiple images
        toast.loading(`Preparing ${images.length} downloads...`);
        
        for (let i = 0; i < images.length; i++) {
            const filename = `${media.title.replace(/[^a-z0-9]/gi, '_').toLowerCase()}_${i + 1}.jpg`;
            await downloadImage(images[i], filename);
            
            // Add small delay between downloads
            if (i < images.length - 1) {
                await new Promise(resolve => setTimeout(resolve, 500));
            }
        }
        
        toast.dismiss();
        toast.success(`${images.length} images downloaded!`);
    }
};

/**
 * Share media via Web Share API or fallback to clipboard
 */
export const shareMedia = async (media: MediaItem) => {
    const shareData = {
        title: media.title,
        text: media.description,
        url: window.location.href + `?media=${media.id}`
    };

    try {
        // Check if Web Share API is supported
        if (navigator.share) {
            await navigator.share(shareData);
            toast.success('Shared successfully!');
        } else {
            // Fallback to clipboard
            await copyToClipboard(shareData);
        }
    } catch (error: any) {
        if (error.name !== 'AbortError') {
            console.error('Share error:', error);
            // Fallback to clipboard if share fails
            await copyToClipboard(shareData);
        }
    }
};

/**
 * Copy share data to clipboard
 */
const copyToClipboard = async (shareData: { title: string; text: string; url: string }) => {
    try {
        const shareText = `${shareData.title}\n\n${shareData.text}\n\n${shareData.url}`;
        
        if (navigator.clipboard) {
            await navigator.clipboard.writeText(shareText);
            toast.success('Link copied to clipboard!');
        } else {
            // Fallback for older browsers
            const textArea = document.createElement('textarea');
            textArea.value = shareText;
            document.body.appendChild(textArea);
            textArea.select();
            document.execCommand('copy');
            document.body.removeChild(textArea);
            toast.success('Link copied to clipboard!');
        }
    } catch (error) {
        console.error('Clipboard error:', error);
        toast.error('Failed to copy link');
    }
};

/**
 * Share to specific social media platforms
 */
export const shareToSocialMedia = (media: MediaItem, platform: 'facebook' | 'twitter' | 'whatsapp' | 'telegram') => {
    const url = window.location.href + `?media=${media.id}`;
    const text = `${media.title} - ${media.description}`;
    
    let shareUrl = '';
    
    switch (platform) {
        case 'facebook':
            shareUrl = `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(url)}`;
            break;
        case 'twitter':
            shareUrl = `https://twitter.com/intent/tweet?text=${encodeURIComponent(text)}&url=${encodeURIComponent(url)}`;
            break;
        case 'whatsapp':
            shareUrl = `https://wa.me/?text=${encodeURIComponent(text + ' ' + url)}`;
            break;
        case 'telegram':
            shareUrl = `https://t.me/share/url?url=${encodeURIComponent(url)}&text=${encodeURIComponent(text)}`;
            break;
    }
    
    if (shareUrl) {
        window.open(shareUrl, '_blank', 'width=600,height=400');
        toast.success(`Shared to ${platform}!`);
    }
};

/**
 * Get category display name
 */
export const getCategoryName = (category: string): string => {
    const categories: { [key: string]: string } = {
        'tournament': 'Pertandingan',
        'training': 'Latihan',
        'ceremony': 'Majlis',
        'general': 'Am',
        'football': 'Bola Sepak',
        'netball': 'Bola Jaring',
        'badminton': 'Badminton',
        'volleyball': 'Bola Tampar',
        'table-tennis': 'Ping Pong',
        'traditional': 'Sukan Rakyat'
    };
    return categories[category] || category;
};

/**
 * Get category color classes
 */
export const getCategoryColor = (category: string): string => {
    const colors: { [key: string]: string } = {
        'tournament': 'bg-red-500/20 text-red-400 border-red-500/30',
        'training': 'bg-blue-500/20 text-blue-400 border-blue-500/30',
        'ceremony': 'bg-purple-500/20 text-purple-400 border-purple-500/30',
        'general': 'bg-green-500/20 text-green-400 border-green-500/30',
        'football': 'bg-red-500/20 text-red-400 border-red-500/30',
        'netball': 'bg-pink-500/20 text-pink-400 border-pink-500/30',
        'badminton': 'bg-blue-500/20 text-blue-400 border-blue-500/30',
        'volleyball': 'bg-orange-500/20 text-orange-400 border-orange-500/30',
        'table-tennis': 'bg-yellow-500/20 text-yellow-400 border-yellow-500/30',
        'traditional': 'bg-green-500/20 text-green-400 border-green-500/30'
    };
    return colors[category] || 'bg-gray-500/20 text-gray-400 border-gray-500/30';
};

/**
 * Format date for display
 */
export const formatDate = (dateString: string): string => {
    return new Date(dateString).toLocaleDateString('ms-MY', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    });
};

/**
 * Format date for gallery cards
 */
export const formatCardDate = (dateString: string): string => {
    const date = new Date(dateString);
    return date.toLocaleDateString('ms-MY', {
        day: 'numeric',
        month: 'long',
        year: 'numeric'
    });
};
