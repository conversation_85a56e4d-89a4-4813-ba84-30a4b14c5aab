{"private": true, "type": "module", "scripts": {"build": "vite build", "dev": "vite", "format": "prettier --write resources/", "format:check": "prettier --check resources/", "lint": "eslint . --fix", "types": "tsc --noEmit"}, "devDependencies": {"@eslint/js": "^9.19.0", "@types/node": "^22.13.5", "@types/react-router-dom": "^5.3.3", "autoprefixer": "^10.4.21", "eslint": "^9.17.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-react": "^7.37.3", "eslint-plugin-react-hooks": "^5.1.0", "postcss": "^8.5.6", "prettier": "^3.4.2", "prettier-plugin-organize-imports": "^4.1.0", "prettier-plugin-tailwindcss": "^0.6.11", "tailwindcss": "^3.4.17", "tsx": "^4.20.3", "typescript-eslint": "^8.23.0"}, "dependencies": {"@headlessui/react": "^2.2.0", "@hookform/resolvers": "^5.1.1", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-collapsible": "^1.1.3", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-navigation-menu": "^1.2.5", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toggle": "^1.1.2", "@radix-ui/react-toggle-group": "^1.1.2", "@radix-ui/react-tooltip": "^1.1.8", "@sportsgram/brackets": "^0.0.1", "@tanstack/react-query": "^5.83.0", "@types/react": "^19.0.3", "@types/react-dom": "^19.0.2", "@vitejs/plugin-react": "^4.3.4", "axios": "^1.10.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "concurrently": "^9.0.1", "globals": "^15.14.0", "laravel-vite-plugin": "^1.0", "lucide-react": "^0.475.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.60.0", "react-hot-toast": "^2.5.2", "react-router-dom": "^7.6.3", "tailwind-merge": "^3.0.1", "tailwindcss-animate": "^1.0.7", "typescript": "^5.7.2", "vite": "^6.0", "zod": "^4.0.2"}, "optionalDependencies": {"@rollup/rollup-linux-x64-gnu": "4.9.5", "lightningcss-linux-x64-gnu": "^1.29.1"}}