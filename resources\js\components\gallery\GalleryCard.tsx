import React from 'react';
import { Video, Image, Calendar, Eye, Play, Download, Share2, Edit, Trash2 } from 'lucide-react';
import OptimizedImage from '@/components/OptimizedImage';
import GalleryCarousel from './GalleryCarousel';
import { getCategoryName, getCategoryColor, formatCardDate } from '@/utils/galleryUtils';
import { useAuth } from '@/contexts/auth-context';

interface MediaItem {
    id: number;
    title: string;
    description: string;
    file_url: string;
    file_urls?: string[];
    image_count?: number;
    file_type: 'image' | 'video';
    category: string;
    zone: string;
    views: number;
    uploaded_at: string;
    uploader?: string;
}

interface GalleryCardProps {
    item: MediaItem;
    onView: (item: MediaItem) => void;
    onEdit?: (item: MediaItem) => void;
    onDelete?: (item: MediaItem) => void;
    onDownload: (item: MediaItem) => void;
    onShare: (item: MediaItem) => void;
}

const GalleryCard: React.FC<GalleryCardProps> = ({
    item,
    onView,
    onEdit,
    onDelete,
    onDownload,
    onShare
}) => {
    const { isAuthenticated, user } = useAuth();

    // Check if user can edit/delete this item
    const canModify = isAuthenticated && (
        user?.role === 'superadmin' || 
        user?.role === 'admin' || 
        (user?.role === 'zone' && user?.zone === item.zone)
    );

    return (
        <div className="group bg-black/60 backdrop-blur-xl rounded-3xl 2xl:rounded-3xl xl:rounded-2xl lg:rounded-2xl md:rounded-xl sm:rounded-lg border-2 border-yellow-400/30 overflow-hidden shadow-2xl hover:border-yellow-400/60 transition-all duration-300 hover:scale-105">
            <div className="relative overflow-hidden">
                {/* Use carousel for multiple images, single image for single image */}
                {item.file_urls && item.file_urls.length > 1 ? (
                    <GalleryCarousel
                        images={item.file_urls}
                        title={item.title}
                        className="w-full h-64 2xl:h-80 xl:h-72 lg:h-64 md:h-56 sm:h-48"
                        showDots={true}
                        showArrows={true}
                        autoPlay={false}
                        autoPlayInterval={5000}
                    />
                ) : (
                    <OptimizedImage
                        src={item.file_urls && item.file_urls.length > 0 ? item.file_urls[0] : item.file_url}
                        alt={item.title}
                        className="w-full h-64 2xl:h-80 xl:h-72 lg:h-64 md:h-56 sm:h-48 object-cover group-hover:scale-110 transition-transform duration-500"
                        lazy={true}
                        enableCache={true}
                    />
                )}
                <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-colors duration-300"></div>

                {/* Media Type Badge */}
                <div className="absolute top-4 2xl:top-6 xl:top-5 lg:top-4 md:top-3 sm:top-2 left-4 2xl:left-6 xl:left-5 lg:left-4 md:left-3 sm:left-2">
                    <span className={`px-3 py-1 2xl:px-4 2xl:py-2 xl:px-3 xl:py-1 lg:px-3 lg:py-1 md:px-2 md:py-1 sm:px-2 sm:py-1 rounded-full text-sm 2xl:text-base xl:text-sm lg:text-xs md:text-xs sm:text-xs font-semibold border ${
                        item.file_type === 'video'
                            ? 'bg-red-500/20 text-red-300 border-red-500/30'
                            : 'bg-blue-500/20 text-blue-300 border-blue-500/30'
                    }`}>
                        {item.file_type === 'video' ? (
                            <>
                                <Video className="h-3 w-3 2xl:h-4 2xl:w-4 xl:h-3 xl:w-3 lg:h-3 lg:w-3 md:h-2 md:w-2 sm:h-2 sm:w-2 inline mr-1" />
                                <span className="hidden sm:inline">Video</span>
                            </>
                        ) : (
                            <>
                                <Image className="h-3 w-3 2xl:h-4 2xl:w-4 xl:h-3 xl:w-3 lg:h-3 lg:w-3 md:h-2 md:w-2 sm:h-2 sm:w-2 inline mr-1" />
                                <span className="hidden sm:inline">
                                    {item.file_urls && item.file_urls.length > 1 
                                        ? `${item.file_urls.length} Foto` 
                                        : 'Foto'
                                    }
                                </span>
                                <span className="sm:hidden">
                                    {item.file_urls && item.file_urls.length > 1 
                                        ? item.file_urls.length 
                                        : '📷'
                                    }
                                </span>
                            </>
                        )}
                    </span>
                </div>

                {/* Category Badge */}
                <div className="absolute top-4 2xl:top-6 xl:top-5 lg:top-4 md:top-3 sm:top-2 right-4 2xl:right-6 xl:right-5 lg:right-4 md:right-3 sm:right-2">
                    <span className={`px-3 py-1 2xl:px-4 2xl:py-2 xl:px-3 xl:py-1 lg:px-3 lg:py-1 md:px-2 md:py-1 sm:px-2 sm:py-1 rounded-full text-sm 2xl:text-base xl:text-sm lg:text-xs md:text-xs sm:text-xs font-semibold border ${getCategoryColor(item.category)}`}>
                        <span className="hidden md:inline">{getCategoryName(item.category)}</span>
                        <span className="md:hidden">🏆</span>
                    </span>
                </div>

                {/* Play Button for Videos */}
                {item.file_type === 'video' && (
                    <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                        <div className="bg-white/90 backdrop-blur-sm rounded-full p-4 2xl:p-6 xl:p-5 lg:p-4 md:p-3 sm:p-2 hover:bg-white transition-colors duration-300">
                            <Play className="h-8 w-8 2xl:h-12 2xl:w-12 xl:h-10 xl:w-10 lg:h-8 lg:w-8 md:h-6 md:w-6 sm:h-4 sm:w-4 text-red-600" />
                        </div>
                    </div>
                )}

                {/* Zone Badge */}
                <div className="absolute bottom-4 2xl:bottom-6 xl:bottom-5 lg:bottom-4 md:bottom-3 sm:bottom-2 left-4 2xl:left-6 xl:left-5 lg:left-4 md:left-3 sm:left-2">
                    <span className="px-3 py-1 2xl:px-4 2xl:py-2 xl:px-3 xl:py-1 lg:px-3 lg:py-1 md:px-2 md:py-1 sm:px-2 sm:py-1 rounded-full text-sm 2xl:text-base xl:text-sm lg:text-xs md:text-xs sm:text-xs font-semibold bg-yellow-500/20 text-yellow-300 border border-yellow-500/30">
                        {item.zone}
                    </span>
                </div>
            </div>

            <div className="p-6 2xl:p-8 xl:p-7 lg:p-6 md:p-5 sm:p-4">
                <h3 className="font-bold text-xl 2xl:text-2xl xl:text-xl lg:text-lg md:text-base sm:text-sm text-white mb-3 2xl:mb-4 xl:mb-3 lg:mb-3 md:mb-2 sm:mb-2 group-hover:text-yellow-400 transition-colors line-clamp-2">
                    {item.title}
                </h3>
                <p className="text-gray-300 text-sm 2xl:text-base xl:text-sm lg:text-xs md:text-xs sm:text-xs mb-4 2xl:mb-6 xl:mb-5 lg:mb-4 md:mb-3 sm:mb-2 line-clamp-2">
                    {item.description}
                </p>

                <div className="flex items-center justify-between text-sm 2xl:text-base xl:text-sm lg:text-xs md:text-xs sm:text-xs text-gray-400 mb-4 2xl:mb-6 xl:mb-5 lg:mb-4 md:mb-3 sm:mb-2">
                    <div className="flex items-center space-x-1">
                        <Calendar className="h-4 w-4 2xl:h-5 2xl:w-5 xl:h-4 xl:w-4 lg:h-3 lg:w-3 md:h-3 md:w-3 sm:h-2 sm:w-2" />
                        <span className="hidden md:inline">{formatCardDate(item.uploaded_at)}</span>
                        <span className="md:hidden">📅</span>
                    </div>
                    <div className="flex items-center space-x-1">
                        <Eye className="h-4 w-4 2xl:h-5 2xl:w-5 xl:h-4 xl:w-4 lg:h-3 lg:w-3 md:h-3 md:w-3 sm:h-2 sm:w-2" />
                        <span>{item.views.toLocaleString()}</span>
                    </div>
                </div>

                {/* Action Buttons */}
                <div className="flex items-center space-x-2 2xl:space-x-3 xl:space-x-2 lg:space-x-2 md:space-x-1 sm:space-x-1">
                    <button 
                        onClick={() => onView(item)}
                        className="flex-1 bg-yellow-500/20 hover:bg-yellow-500/30 text-yellow-300 px-4 py-2 2xl:px-6 2xl:py-3 xl:px-5 xl:py-2 lg:px-4 lg:py-2 md:px-3 md:py-1 sm:px-2 sm:py-1 rounded-lg 2xl:rounded-xl xl:rounded-lg lg:rounded-lg md:rounded-md sm:rounded-md text-sm 2xl:text-base xl:text-sm lg:text-xs md:text-xs sm:text-xs font-semibold transition-all duration-300 border border-yellow-500/30 hover:border-yellow-500/50"
                    >
                        <Eye className="h-4 w-4 2xl:h-5 2xl:w-5 xl:h-4 xl:w-4 lg:h-3 lg:w-3 md:h-3 md:w-3 sm:h-2 sm:w-2 inline mr-1" />
                        <span className="hidden sm:inline">Lihat</span>
                    </button>
                    
                    <button 
                        onClick={() => onDownload(item)}
                        className="bg-blue-500/20 hover:bg-blue-500/30 text-blue-300 px-3 py-2 2xl:px-4 2xl:py-3 xl:px-3 xl:py-2 lg:px-3 lg:py-2 md:px-2 md:py-1 sm:px-2 sm:py-1 rounded-lg 2xl:rounded-xl xl:rounded-lg lg:rounded-lg md:rounded-md sm:rounded-md text-sm 2xl:text-base xl:text-sm lg:text-xs md:text-xs sm:text-xs transition-all duration-300 border border-blue-500/30 hover:border-blue-500/50"
                    >
                        <Download className="h-4 w-4 2xl:h-5 2xl:w-5 xl:h-4 xl:w-4 lg:h-3 lg:w-3 md:h-3 md:w-3 sm:h-2 sm:w-2" />
                    </button>
                    
                    <button 
                        onClick={() => onShare(item)}
                        className="bg-green-500/20 hover:bg-green-500/30 text-green-300 px-3 py-2 2xl:px-4 2xl:py-3 xl:px-3 xl:py-2 lg:px-3 lg:py-2 md:px-2 md:py-1 sm:px-2 sm:py-1 rounded-lg 2xl:rounded-xl xl:rounded-lg lg:rounded-lg md:rounded-md sm:rounded-md text-sm 2xl:text-base xl:text-sm lg:text-xs md:text-xs sm:text-xs transition-all duration-300 border border-green-500/30 hover:border-green-500/50"
                    >
                        <Share2 className="h-4 w-4 2xl:h-5 2xl:w-5 xl:h-4 xl:w-4 lg:h-3 lg:w-3 md:h-3 md:w-3 sm:h-2 sm:w-2" />
                    </button>

                    {/* Edit/Delete buttons for authorized users */}
                    {canModify && onEdit && (
                        <button 
                            onClick={() => onEdit(item)}
                            className="bg-purple-500/20 hover:bg-purple-500/30 text-purple-300 px-3 py-2 2xl:px-4 2xl:py-3 xl:px-3 xl:py-2 lg:px-3 lg:py-2 md:px-2 md:py-1 sm:px-2 sm:py-1 rounded-lg 2xl:rounded-xl xl:rounded-lg lg:rounded-lg md:rounded-md sm:rounded-md text-sm 2xl:text-base xl:text-sm lg:text-xs md:text-xs sm:text-xs transition-all duration-300 border border-purple-500/30 hover:border-purple-500/50"
                        >
                            <Edit className="h-4 w-4 2xl:h-5 2xl:w-5 xl:h-4 xl:w-4 lg:h-3 lg:w-3 md:h-3 md:w-3 sm:h-2 sm:w-2" />
                        </button>
                    )}

                    {canModify && onDelete && (
                        <button 
                            onClick={() => onDelete(item)}
                            className="bg-red-500/20 hover:bg-red-500/30 text-red-300 px-3 py-2 2xl:px-4 2xl:py-3 xl:px-3 xl:py-2 lg:px-3 lg:py-2 md:px-2 md:py-1 sm:px-2 sm:py-1 rounded-lg 2xl:rounded-xl xl:rounded-lg lg:rounded-lg md:rounded-md sm:rounded-md text-sm 2xl:text-base xl:text-sm lg:text-xs md:text-xs sm:text-xs transition-all duration-300 border border-red-500/30 hover:border-red-500/50"
                        >
                            <Trash2 className="h-4 w-4 2xl:h-5 2xl:w-5 xl:h-4 xl:w-4 lg:h-3 lg:w-3 md:h-3 md:w-3 sm:h-2 sm:w-2" />
                        </button>
                    )}
                </div>
            </div>
        </div>
    );
};

export default GalleryCard;
