import React from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle } from '@/components/ui/dialog';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { User, MapPin, Trophy, Users, Calendar } from 'lucide-react';

interface Player {
    id: number;
    name: string;
    phone: string;
    player_type: 'main' | 'substitute';
    status: string;
    zone: string | null;
    zone_name: string | null;
    sport: string | null;
    sport_id: number | null;
    team_leader_name: string | null;
    team_leader_phone: string | null;
    created_at: string;
}

interface PlayerDetailsModalProps {
    player: Player | null;
    isOpen: boolean;
    onClose: () => void;
}

export default function PlayerDetailsModal({ player, isOpen, onClose }: PlayerDetailsModalProps) {
    if (!player) return null;

    const getStatusBadge = (status: string) => {
        const statusConfig = {
            'active': { label: 'Aktif', className: 'bg-green-100 text-green-800 border-green-200' },
            'inactive': { label: 'Tidak Aktif', className: 'bg-red-100 text-red-800 border-red-200' },
            'pending': { label: 'Menunggu', className: 'bg-yellow-100 text-yellow-800 border-yellow-200' },
        };

        const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.active;
        return <Badge className={`${config.className} border font-medium`}>{config.label}</Badge>;
    };

    const getPlayerTypeBadge = (type: 'main' | 'substitute') => {
        return type === 'main'
            ? <Badge className="bg-blue-100 text-blue-800 border-blue-200 border font-medium">Pemain Utama</Badge>
            : <Badge className="bg-gray-100 text-gray-800 border-gray-200 border font-medium">Pemain Simpanan</Badge>;
    };

    return (
        <Dialog open={isOpen} onOpenChange={onClose}>
            <DialogContent className="max-w-6xl max-h-[90vh] w-[98vw] sm:w-full p-0 flex flex-col">
                {/* Fixed Header */}
                <DialogHeader className="flex-shrink-0 px-4 sm:px-6 py-4 border-b bg-white">
                    <DialogTitle className="flex items-center gap-3 text-base sm:text-lg">
                        <User className="w-5 h-5 sm:w-6 sm:h-6 text-blue-600" />
                        Maklumat Peserta
                    </DialogTitle>
                </DialogHeader>

                {/* Scrollable Content */}
                <div className="flex-1 overflow-y-auto px-4 sm:px-6 py-4 sm:py-6">
                    <div className="space-y-4 sm:space-y-6">
                    {/* Basic Information */}
                    <Card>
                        <CardHeader className="pb-3 sm:pb-6">
                            <CardTitle className="text-base sm:text-lg">Maklumat Asas</CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
                                <div className="space-y-3">
                                    <label className="text-sm font-medium text-gray-700">Nama Penuh</label>
                                    <div className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
                                        <User className="w-4 h-4 text-gray-500 flex-shrink-0" />
                                        <span className="font-medium text-gray-900 break-words">{player.name}</span>
                                    </div>
                                </div>

                                <div className="space-y-3">
                                    <label className="text-sm font-medium text-gray-700">Tarikh Pendaftaran</label>
                                    <div className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
                                        <Calendar className="w-4 h-4 text-gray-500 flex-shrink-0" />
                                        <span className="text-gray-900">{new Date(player.created_at).toLocaleDateString('ms-MY')}</span>
                                    </div>
                                </div>
                            </div>
                        </CardContent>
                    </Card>

                    {/* Zone and Sport Information */}
                    <Card>
                        <CardHeader className="pb-3 sm:pb-6">
                            <CardTitle className="text-base sm:text-lg">Maklumat Zon & Sukan</CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
                                <div className="space-y-3">
                                    <label className="text-sm font-medium text-gray-700">Zon</label>
                                    <div className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
                                        <MapPin className="w-4 h-4 text-gray-500 flex-shrink-0" />
                                        <Badge variant="outline" className="bg-white">
                                            {player.zone_name || player.zone || '-'}
                                        </Badge>
                                    </div>
                                </div>

                                <div className="space-y-3">
                                    <label className="text-sm font-medium text-gray-700">Sukan</label>
                                    <div className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
                                        <Trophy className="w-4 h-4 text-gray-500 flex-shrink-0" />
                                        <span className="font-medium text-gray-900 break-words">{player.sport || '-'}</span>
                                    </div>
                                </div>

                                <div className="space-y-3 sm:col-span-2 lg:col-span-1">
                                    <label className="text-sm font-medium text-gray-700">Jenis Pemain</label>
                                    <div className="p-3 bg-gray-50 rounded-lg">
                                        {getPlayerTypeBadge(player.player_type)}
                                    </div>
                                </div>
                            </div>
                        </CardContent>
                    </Card>

                    {/* Team Leader Information */}
                    {player.team_leader_name && (
                        <Card>
                            <CardHeader className="pb-3 sm:pb-6">
                                <CardTitle className="text-base sm:text-lg">Maklumat Ketua Pasukan</CardTitle>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <div className="grid grid-cols-1 gap-6">
                                    <div className="space-y-3">
                                        <label className="text-sm font-medium text-gray-700">Nama Ketua Pasukan</label>
                                        <div className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
                                            <Users className="w-4 h-4 text-gray-500 flex-shrink-0" />
                                            <span className="font-medium text-gray-900 break-words">{player.team_leader_name}</span>
                                        </div>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>
                    )}

                    {/* Note for read-only */}
                    <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                        <div className="flex items-start gap-3">
                            <div className="w-2 h-2 bg-blue-500 rounded-full flex-shrink-0 mt-2"></div>
                            <div>
                                <p className="text-sm text-blue-800 font-medium mb-1">Nota Penting</p>
                                <p className="text-sm text-blue-700 leading-relaxed">
                                    Ini adalah paparan maklumat sahaja. Untuk mengemaskini maklumat peserta,
                                    sila hubungi admin zon yang berkaitan.
                                </p>
                            </div>
                        </div>
                    </div>
                    </div>
                </div>
                {/* End Scrollable Content */}

                {/* Fixed Footer */}
                <div className="flex-shrink-0 px-4 sm:px-6 py-3 sm:py-4 border-t bg-gray-50">
                    <div className="flex justify-end">
                        <Button
                            variant="outline"
                            onClick={onClose}
                            className="min-w-[80px]"
                        >
                            Tutup
                        </Button>
                    </div>
                </div>
            </DialogContent>
        </Dialog>
    );
}
