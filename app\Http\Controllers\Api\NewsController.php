<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\News;
use Illuminate\Http\Request;

class NewsController extends Controller
{
    /**
     * Display a listing of news articles
     */
    public function index(Request $request)
    {
        $query = News::query();

        // Filter by status (only published for public API)
        $query->where('is_published', true);
        
        // Filter by category if specified
        if ($request->has('category')) {
            $query->where('category', $request->category);
        }
        
        // Search by title or content
        if ($request->has('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                  ->orWhere('excerpt', 'like', "%{$search}%")
                  ->orWhere('content', 'like', "%{$search}%");
            });
        }
        
        // Filter by important
        if ($request->has('important')) {
            $query->where('is_important', $request->boolean('important'));
        }
        
        // Pagination
        $perPage = $request->get('per_page', 10);
        $news = $query->orderBy('published_at', 'desc')
                     ->paginate($perPage);
        
        // Transform the data
        $news->getCollection()->transform(function ($article) {
            return [
                'id' => $article->id,
                'title' => $article->title,
                'slug' => $article->slug,
                'excerpt' => $article->excerpt,
                'featured_image' => $article->featured_image,
                'category' => $article->category,
                'tags' => json_decode($article->tags, true),
                'is_important' => $article->is_important,
                'views' => $article->views,
                'published_at' => $article->published_at->format('Y-m-d H:i:s'),
                'author' => $article->author,
            ];
        });

        return response()->json($news);
    }

    /**
     * Display the specified news article
     */
    public function show($id)
    {
        $article = News::where('is_published', true)
                      ->findOrFail($id);

        return response()->json([
            'id' => $article->id,
            'title' => $article->title,
            'slug' => $article->slug,
            'excerpt' => $article->excerpt,
            'content' => $article->content,
            'featured_image' => $article->featured_image,
            'category' => $article->category,
            'tags' => json_decode($article->tags, true),
            'is_important' => $article->is_important,
            'views' => $article->views,
            'published_at' => $article->published_at->format('Y-m-d H:i:s'),
            'author' => $article->author,
            'zone' => $article->zone,
        ]);
    }

    /**
     * Increment view count for news article
     */
    public function incrementView($id)
    {
        $article = News::where('is_published', true)->findOrFail($id);
        $article->increment('views');

        return response()->json([
            'success' => true,
            'views' => $article->views
        ]);
    }

    /**
     * Get featured news articles
     */
    public function featured(Request $request)
    {
        $limit = $request->get('limit', 5);

        $news = News::where('is_published', true)
                   ->where('is_important', true)
                   ->orderBy('published_at', 'desc')
                   ->limit($limit)
                   ->get();

        $news = $news->map(function ($article) {
            return [
                'id' => $article->id,
                'title' => $article->title,
                'slug' => $article->slug,
                'excerpt' => $article->excerpt,
                'featured_image' => $article->featured_image,
                'category' => $article->category,
                'published_at' => $article->published_at->format('Y-m-d H:i:s'),
                'author' => $article->author,
            ];
        });

        return response()->json($news);
    }

    /**
     * Get news categories
     */
    public function categories()
    {
        $categories = News::where('is_published', true)
                         ->distinct()
                         ->pluck('category')
                         ->filter()
                         ->values();

        return response()->json($categories);
    }
}
