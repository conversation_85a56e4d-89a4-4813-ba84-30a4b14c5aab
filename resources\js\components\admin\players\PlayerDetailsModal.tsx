import React from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle } from '@/components/ui/dialog';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { User, Phone, MapPin, Trophy, Users, Calendar } from 'lucide-react';

interface Player {
    id: number;
    name: string;
    phone: string;
    player_type: 'main' | 'substitute';
    status: string;
    zone: string | null;
    zone_name: string | null;
    sport: string | null;
    sport_id: number | null;
    team_leader_name: string | null;
    team_leader_phone: string | null;
    created_at: string;
}

interface PlayerDetailsModalProps {
    player: Player | null;
    isOpen: boolean;
    onClose: () => void;
}

export default function PlayerDetailsModal({ player, isOpen, onClose }: PlayerDetailsModalProps) {
    if (!player) return null;

    const getStatusBadge = (status: string) => {
        const statusConfig = {
            'active': { label: 'Aktif', className: 'bg-green-100 text-green-800' },
            'inactive': { label: 'Tidak Aktif', className: 'bg-red-100 text-red-800' },
            'pending': { label: 'Menunggu', className: 'bg-yellow-100 text-yellow-800' },
        };
        
        const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.active;
        return <Badge className={config.className}>{config.label}</Badge>;
    };

    const getPlayerTypeBadge = (type: 'main' | 'substitute') => {
        return type === 'main' 
            ? <Badge variant="default">Pemain Utama</Badge>
            : <Badge variant="outline">Pemain Simpanan</Badge>;
    };

    return (
        <Dialog open={isOpen} onOpenChange={onClose}>
            <DialogContent className="max-w-2xl">
                <DialogHeader>
                    <DialogTitle className="flex items-center gap-3">
                        <User className="w-6 h-6 text-blue-600" />
                        Maklumat Peserta
                    </DialogTitle>
                </DialogHeader>

                <div className="space-y-6">
                    {/* Basic Information */}
                    <Card>
                        <CardHeader>
                            <CardTitle className="text-lg">Maklumat Asas</CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div className="space-y-2">
                                    <label className="text-sm font-medium text-gray-600">Nama Penuh</label>
                                    <div className="flex items-center gap-2">
                                        <User className="w-4 h-4 text-gray-400" />
                                        <span className="font-medium">{player.name}</span>
                                    </div>
                                </div>
                                
                                <div className="space-y-2">
                                    <label className="text-sm font-medium text-gray-600">No. Telefon</label>
                                    <div className="flex items-center gap-2">
                                        <Phone className="w-4 h-4 text-gray-400" />
                                        <span>{player.phone}</span>
                                    </div>
                                </div>
                                
                                <div className="space-y-2">
                                    <label className="text-sm font-medium text-gray-600">Status</label>
                                    <div>{getStatusBadge(player.status)}</div>
                                </div>
                                
                                <div className="space-y-2">
                                    <label className="text-sm font-medium text-gray-600">Tarikh Pendaftaran</label>
                                    <div className="flex items-center gap-2">
                                        <Calendar className="w-4 h-4 text-gray-400" />
                                        <span>{player.created_at}</span>
                                    </div>
                                </div>
                            </div>
                        </CardContent>
                    </Card>

                    {/* Zone and Sport Information */}
                    <Card>
                        <CardHeader>
                            <CardTitle className="text-lg">Maklumat Zon & Sukan</CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div className="space-y-2">
                                    <label className="text-sm font-medium text-gray-600">Zon</label>
                                    <div className="flex items-center gap-2">
                                        <MapPin className="w-4 h-4 text-gray-400" />
                                        <Badge variant="outline">
                                            {player.zone_name || player.zone || '-'}
                                        </Badge>
                                    </div>
                                </div>
                                
                                <div className="space-y-2">
                                    <label className="text-sm font-medium text-gray-600">Sukan</label>
                                    <div className="flex items-center gap-2">
                                        <Trophy className="w-4 h-4 text-gray-400" />
                                        <span className="font-medium">{player.sport || '-'}</span>
                                    </div>
                                </div>
                                
                                <div className="space-y-2">
                                    <label className="text-sm font-medium text-gray-600">Jenis Pemain</label>
                                    <div>{getPlayerTypeBadge(player.player_type)}</div>
                                </div>
                            </div>
                        </CardContent>
                    </Card>

                    {/* Team Leader Information */}
                    {player.team_leader_name && (
                        <Card>
                            <CardHeader>
                                <CardTitle className="text-lg">Maklumat Ketua Pasukan</CardTitle>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <div className="space-y-2">
                                        <label className="text-sm font-medium text-gray-600">Nama Ketua Pasukan</label>
                                        <div className="flex items-center gap-2">
                                            <Users className="w-4 h-4 text-gray-400" />
                                            <span className="font-medium">{player.team_leader_name}</span>
                                        </div>
                                    </div>
                                    
                                    <div className="space-y-2">
                                        <label className="text-sm font-medium text-gray-600">No. Telefon Ketua</label>
                                        <div className="flex items-center gap-2">
                                            <Phone className="w-4 h-4 text-gray-400" />
                                            <span>{player.team_leader_phone}</span>
                                        </div>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>
                    )}

                    {/* Note for read-only */}
                    <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                        <div className="flex items-center gap-2">
                            <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                            <p className="text-sm text-blue-700">
                                <strong>Nota:</strong> Ini adalah paparan maklumat sahaja. 
                                Untuk mengemaskini maklumat peserta, sila hubungi admin zon yang berkaitan.
                            </p>
                        </div>
                    </div>
                </div>
            </DialogContent>
        </Dialog>
    );
}
