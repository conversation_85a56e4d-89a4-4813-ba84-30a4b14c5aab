<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\SportMatch;
use App\Models\SportsSchedule;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class PublicJadualController extends Controller
{
    /**
     * Get all sports schedules for public display
     */
    public function getSportsSchedules(Request $request): JsonResponse
    {
        $query = SportsSchedule::with(['sport', 'matches' => function($query) {
                $query->with('sport')->orderBy('match_date')->orderBy('match_time');
            }])
            ->active()
            ->ordered();

        // Filter by sport if provided
        if ($request->filled('sport_id')) {
            $query->where('sport_id', $request->sport_id);
        }

        // Filter by date range if provided
        if ($request->filled('start_date')) {
            $query->where('start_date', '>=', $request->start_date);
        }

        if ($request->filled('end_date')) {
            $query->where('end_date', '<=', $request->end_date);
        }

        $schedules = $query->get();

        return response()->json([
            'data' => $schedules
        ]);
    }

    /**
     * Get all matches for public display
     */
    public function getMatches(Request $request): JsonResponse
    {
        $query = SportMatch::with(['sport', 'sportsSchedule'])
            ->orderedByDateTime();

        // Filter by sport if provided
        if ($request->filled('sport_id')) {
            $query->where('sport_id', $request->sport_id);
        }

        // Filter by sports schedule if provided
        if ($request->filled('sports_schedule_id')) {
            $query->where('sports_schedule_id', $request->sports_schedule_id);
        }

        // Filter by status if provided
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // Filter by date range if provided
        if ($request->filled('start_date')) {
            $query->where('match_date', '>=', $request->start_date);
        }

        if ($request->filled('end_date')) {
            $query->where('match_date', '<=', $request->end_date);
        }

        // Search by title if provided
        if ($request->filled('search')) {
            $query->where('title', 'like', '%' . $request->search . '%');
        }

        // Only show non-cancelled matches for public
        $query->where('status', '!=', 'cancelled');

        $matches = $query->get();

        return response()->json([
            'data' => $matches
        ]);
    }

    /**
     * Get matches by schedule for public display
     */
    public function getMatchesBySchedule(Request $request, $scheduleId): JsonResponse
    {
        $schedule = SportsSchedule::with(['sport'])->findOrFail($scheduleId);

        $matches = SportMatch::with(['sport', 'sportsSchedule'])
            ->where('sports_schedule_id', $scheduleId)
            ->where('status', '!=', 'cancelled')
            ->orderedByBil()
            ->orderedByDateTime()
            ->get();

        return response()->json([
            'schedule' => $schedule,
            'matches' => $matches
        ]);
    }

    /**
     * Get tournament bracket matches for public display
     */
    public function getTournamentBrackets(Request $request): JsonResponse
    {
        $query = SportMatch::with(['sport', 'sportsSchedule'])
            ->whereNotNull('bracket_round')
            ->orderedByDateTime();

        // Filter by sport if provided
        if ($request->filled('sport_id')) {
            $query->where('sport_id', $request->sport_id);
        }

        // Filter by sports schedule if provided
        if ($request->filled('sports_schedule_id')) {
            $query->where('sports_schedule_id', $request->sports_schedule_id);
        }

        // Only show non-cancelled matches for public
        $query->where('status', '!=', 'cancelled');

        $matches = $query->get();

        // Group by sports schedule
        $groupedBrackets = $matches->groupBy('sports_schedule.schedule_name');

        return response()->json([
            'data' => $groupedBrackets
        ]);
    }

    /**
     * Get live/ongoing matches
     */
    public function getLiveMatches(): JsonResponse
    {
        $liveMatches = SportMatch::with(['sport', 'sportsSchedule'])
            ->where('status', 'ongoing')
            ->orderedByDateTime()
            ->get();

        return response()->json([
            'data' => $liveMatches
        ]);
    }

    /**
     * Get upcoming matches (next 7 days)
     */
    public function getUpcomingMatches(): JsonResponse
    {
        $upcomingMatches = SportMatch::with(['sport', 'sportsSchedule'])
            ->where('status', 'scheduled')
            ->where('match_date', '>=', now()->toDateString())
            ->where('match_date', '<=', now()->addDays(7)->toDateString())
            ->orderedByDateTime()
            ->limit(10)
            ->get();

        return response()->json([
            'data' => $upcomingMatches
        ]);
    }

    /**
     * Get match statistics for public display
     */
    public function getMatchStatistics(): JsonResponse
    {
        $totalMatches = SportMatch::count();
        $completedMatches = SportMatch::where('status', 'completed')->count();
        $upcomingMatches = SportMatch::where('status', 'scheduled')->count();
        $ongoingMatches = SportMatch::where('status', 'ongoing')->count();

        $totalSchedules = SportsSchedule::active()->count();
        $activeSports = SportMatch::distinct('sport_id')->count();

        return response()->json([
            'total_matches' => $totalMatches,
            'completed_matches' => $completedMatches,
            'upcoming_matches' => $upcomingMatches,
            'ongoing_matches' => $ongoingMatches,
            'total_schedules' => $totalSchedules,
            'active_sports' => $activeSports,
        ]);
    }
}
