import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { useAuth } from './auth-context';
import api from '@/lib/axios';

interface Zone {
    id: number;
    code: string;
    name: string;
    description?: string;
    logo_url?: string;
    color_primary: string;
    color_secondary: string;
    leader_id?: number;
    leader_name?: string;
    leader_contact?: string;
    leader?: {
        id: number;
        name: string;
        username: string;
    };
    is_active: boolean;
    display_order: number;
    created_at: string;
    updated_at: string;
}

interface ZoneContextType {
    zone: Zone | null;
    loading: boolean;
    error: string | null;
    refreshZone: () => Promise<void>;
    updateZone: (updatedZone: Zone) => void;
}

const ZoneContext = createContext<ZoneContextType | undefined>(undefined);

interface ZoneProviderProps {
    children: ReactNode;
}

export function ZoneProvider({ children }: ZoneProviderProps) {
    const { user } = useAuth();
    const [zone, setZone] = useState<Zone | null>(null);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState<string | null>(null);

    const fetchZone = async () => {
        if (!user || user.role !== 'zone' || !user.zone) {
            setZone(null);
            return;
        }

        setLoading(true);
        setError(null);

        try {
            const response = await api.get(`/admin/zones/${user.zone}`);
            setZone(response.data);
        } catch (err: any) {
            setError(err.response?.data?.message || 'Failed to fetch zone data');
            console.error('Error fetching zone:', err);
        } finally {
            setLoading(false);
        }
    };

    const refreshZone = async () => {
        await fetchZone();
    };

    const updateZone = (updatedZone: Zone) => {
        setZone(updatedZone);
    };

    useEffect(() => {
        if (user) {
            fetchZone();
        }
    }, [user]);

    const value: ZoneContextType = {
        zone,
        loading,
        error,
        refreshZone,
        updateZone,
    };

    return (
        <ZoneContext.Provider value={value}>
            {children}
        </ZoneContext.Provider>
    );
}

export function useZone() {
    const context = useContext(ZoneContext);
    if (context === undefined) {
        throw new Error('useZone must be used within a ZoneProvider');
    }
    return context;
}
