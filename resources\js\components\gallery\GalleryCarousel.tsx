import React, { useState, useEffect } from 'react';
import { ChevronLeft, ChevronRight } from 'lucide-react';
import OptimizedImage from '@/components/OptimizedImage';

interface GalleryCarouselProps {
    images: string[];
    title: string;
    className?: string;
    showDots?: boolean;
    showArrows?: boolean;
    autoPlay?: boolean;
    autoPlayInterval?: number;
}

const GalleryCarousel: React.FC<GalleryCarouselProps> = ({
    images,
    title,
    className = '',
    showDots = true,
    showArrows = true,
    autoPlay = false,
    autoPlayInterval = 3000
}) => {
    const [currentIndex, setCurrentIndex] = useState(0);

    // If only one image, don't show carousel controls
    const isSingleImage = images.length <= 1;

    // Reset index when images change
    useEffect(() => {
        setCurrentIndex(0);
    }, [images]);

    const goToPrevious = (e?: React.MouseEvent) => {
        e?.stopPropagation();
        if (!isSingleImage) {
            setCurrentIndex((prevIndex) =>
                prevIndex === 0 ? images.length - 1 : prevIndex - 1
            );
        }
    };

    const goToNext = (e?: React.MouseEvent) => {
        e?.stopPropagation();
        if (!isSingleImage) {
            setCurrentIndex((prevIndex) =>
                prevIndex === images.length - 1 ? 0 : prevIndex + 1
            );
        }
    };

    const goToSlide = (index: number, e?: React.MouseEvent) => {
        e?.stopPropagation();
        if (index >= 0 && index < images.length) {
            setCurrentIndex(index);
        }
    };

    // Auto play functionality
    useEffect(() => {
        if (autoPlay && !isSingleImage && images.length > 1) {
            const interval = setInterval(() => {
                setCurrentIndex((prevIndex) =>
                    prevIndex === images.length - 1 ? 0 : prevIndex + 1
                );
            }, autoPlayInterval);
            return () => clearInterval(interval);
        }
    }, [autoPlay, autoPlayInterval, isSingleImage, images.length]);

    if (!images || images.length === 0) {
        return (
            <div className={`relative overflow-hidden bg-gray-200 flex items-center justify-center ${className}`}>
                <span className="text-gray-500">No images available</span>
            </div>
        );
    }

    // Ensure currentIndex is within bounds
    const safeCurrentIndex = Math.max(0, Math.min(currentIndex, images.length - 1));

    return (
        <div className={`relative overflow-hidden group/carousel ${className}`}>
            {/* Main Image Display */}
            <div className="relative w-full h-full">
                <OptimizedImage
                    src={images[safeCurrentIndex]}
                    alt={`${title} - Image ${safeCurrentIndex + 1}`}
                    className="w-full h-full object-cover transition-opacity duration-300"
                    lazy={false}
                    enableCache={true}
                />

                {/* Image Counter */}
                {!isSingleImage && (
                    <div className="absolute top-2 right-2 bg-black/70 text-white text-xs px-2 py-1 rounded-full z-10">
                        {safeCurrentIndex + 1}/{images.length}
                    </div>
                )}

                {/* Navigation Arrows */}
                {showArrows && !isSingleImage && (
                    <>
                        <button
                            onClick={(e) => goToPrevious(e)}
                            className="absolute left-2 top-1/2 transform -translate-y-1/2 bg-black/50 hover:bg-black/70 text-white p-2 rounded-full transition-all duration-200 opacity-0 group-hover/carousel:opacity-100 z-10"
                            aria-label="Previous image"
                            type="button"
                        >
                            <ChevronLeft className="w-4 h-4" />
                        </button>
                        <button
                            onClick={(e) => goToNext(e)}
                            className="absolute right-2 top-1/2 transform -translate-y-1/2 bg-black/50 hover:bg-black/70 text-white p-2 rounded-full transition-all duration-200 opacity-0 group-hover/carousel:opacity-100 z-10"
                            aria-label="Next image"
                            type="button"
                        >
                            <ChevronRight className="w-4 h-4" />
                        </button>
                    </>
                )}
            </div>

            {/* Dots Indicator */}
            {showDots && !isSingleImage && (
                <div className="absolute bottom-2 left-1/2 transform -translate-x-1/2 flex space-x-2 z-10">
                    {images.map((_, index) => (
                        <button
                            key={index}
                            onClick={(e) => goToSlide(index, e)}
                            className={`w-2 h-2 rounded-full transition-all duration-200 ${
                                index === safeCurrentIndex
                                    ? 'bg-white scale-125'
                                    : 'bg-white/50 hover:bg-white/75'
                            }`}
                            aria-label={`Go to image ${index + 1}`}
                            type="button"
                        />
                    ))}
                </div>
            )}

            {/* Thumbnail Strip (for more than 3 images) */}
            {images.length > 3 && (
                <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/70 to-transparent p-2 z-10">
                    <div className="flex space-x-1 overflow-x-auto scrollbar-hide">
                        {images.map((image, index) => (
                            <button
                                key={index}
                                onClick={(e) => goToSlide(index, e)}
                                className={`flex-shrink-0 w-12 h-8 rounded overflow-hidden border-2 transition-all duration-200 ${
                                    index === currentIndex
                                        ? 'border-yellow-400 scale-110'
                                        : 'border-white/30 hover:border-white/60'
                                }`}
                                type="button"
                            >
                                <OptimizedImage
                                    src={image}
                                    alt={`Thumbnail ${index + 1}`}
                                    className="w-full h-full object-cover"
                                    lazy={true}
                                    enableCache={true}
                                />
                            </button>
                        ))}
                    </div>
                </div>
            )}
        </div>
    );
};

export default GalleryCarousel;
