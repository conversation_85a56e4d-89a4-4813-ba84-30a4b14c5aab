import { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import api from '@/lib/axios';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Calendar, Trophy, MapPin, FileText } from 'lucide-react';
import { toast } from 'react-hot-toast';

const scheduleSchema = z.object({
    sport_id: z.string().min(1, 'Sukan diperlukan'),
    schedule_name: z.string().min(1, 'Nama jadual diperlukan'),
    description: z.string().optional(),
    start_date: z.string().min(1, 'Tarikh mula diperlukan'),
    end_date: z.string().min(1, 'Tarikh tamat diperlukan'),
    main_venue: z.string().optional(),
    display_order: z.number().min(0, 'Susunan paparan mesti 0 atau lebih').optional(),
});

type ScheduleFormData = z.infer<typeof scheduleSchema>;

interface Sport {
    id: number;
    name: string;
}

interface SportsScheduleFormProps {
    schedule?: any;
    onSuccess: () => void;
    onCancel: () => void;
}

export default function SportsScheduleForm({ schedule, onSuccess, onCancel }: SportsScheduleFormProps) {
    const [sports, setSports] = useState<Sport[]>([]);
    const [isLoading, setIsLoading] = useState(false);

    const {
        register,
        handleSubmit,
        setValue,
        watch,
        formState: { errors },
        reset
    } = useForm<ScheduleFormData>({
        resolver: zodResolver(scheduleSchema),
        defaultValues: {
            display_order: 0,
            ...schedule
        }
    });

    useEffect(() => {
        fetchSports();
    }, []);

    useEffect(() => {
        if (schedule) {
            reset({
                sport_id: schedule.sport_id?.toString(),
                schedule_name: schedule.schedule_name,
                description: schedule.description,
                start_date: schedule.start_date,
                end_date: schedule.end_date,
                main_venue: schedule.main_venue,
                display_order: schedule.display_order || 0,
            });
        }
    }, [schedule, reset]);

    const fetchSports = async () => {
        try {
            const response = await api.get('/admin/sports-schedules/form-data/sports');
            setSports(response.data.data || []);
        } catch (error) {
            console.error('Error fetching sports:', error);
            toast.error('Gagal memuat senarai sukan');
        }
    };

    const onSubmit = async (data: ScheduleFormData) => {
        try {
            setIsLoading(true);
            
            const payload = {
                ...data,
                sport_id: parseInt(data.sport_id),
                display_order: data.display_order || 0,
            };

            if (schedule) {
                await api.put(`/admin/sports-schedules/${schedule.id}`, payload);
                toast.success('Jadual sukan berjaya dikemaskini');
            } else {
                await api.post('/admin/sports-schedules', payload);
                toast.success('Jadual sukan berjaya ditambah');
            }

            onSuccess();
        } catch (error: any) {
            console.error('Error saving schedule:', error);
            const message = error.response?.data?.message || 'Gagal menyimpan jadual sukan';
            toast.error(message);
        } finally {
            setIsLoading(false);
        }
    };

    return (
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
            <div className="grid gap-6 md:grid-cols-2">
                {/* Basic Information */}
                <Card>
                    <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                            <Trophy className="h-5 w-5" />
                            Maklumat Asas
                        </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                        <div>
                            <Label htmlFor="sport_id">Sukan *</Label>
                            <Select 
                                value={watch('sport_id') || ''} 
                                onValueChange={(value) => setValue('sport_id', value)}
                            >
                                <SelectTrigger>
                                    <SelectValue placeholder="Pilih sukan" />
                                </SelectTrigger>
                                <SelectContent>
                                    {sports.map((sport) => (
                                        <SelectItem key={sport.id} value={sport.id.toString()}>
                                            {sport.name}
                                        </SelectItem>
                                    ))}
                                </SelectContent>
                            </Select>
                            {errors.sport_id && (
                                <p className="text-sm text-red-600">{errors.sport_id.message}</p>
                            )}
                        </div>

                        <div>
                            <Label htmlFor="schedule_name">Nama Jadual *</Label>
                            <Input
                                id="schedule_name"
                                {...register('schedule_name')}
                                placeholder="Contoh: Acara: Badminton Zon (Mixed Categories)"
                            />
                            {errors.schedule_name && (
                                <p className="text-sm text-red-600">{errors.schedule_name.message}</p>
                            )}
                        </div>

                        <div>
                            <Label htmlFor="description">Penerangan</Label>
                            <Textarea
                                id="description"
                                {...register('description')}
                                placeholder="Penerangan tentang jadual ini..."
                                rows={3}
                            />
                        </div>

                        <div>
                            <Label htmlFor="display_order">Susunan Paparan</Label>
                            <Input
                                id="display_order"
                                type="number"
                                min="0"
                                {...register('display_order', { valueAsNumber: true })}
                                placeholder="0, 1, 2..."
                            />
                            {errors.display_order && (
                                <p className="text-sm text-red-600">{errors.display_order.message}</p>
                            )}
                        </div>
                    </CardContent>
                </Card>

                {/* Date & Venue */}
                <Card>
                    <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                            <Calendar className="h-5 w-5" />
                            Tarikh & Tempat
                        </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                        <div>
                            <Label htmlFor="start_date">Tarikh Mula *</Label>
                            <Input
                                id="start_date"
                                type="date"
                                {...register('start_date')}
                            />
                            {errors.start_date && (
                                <p className="text-sm text-red-600">{errors.start_date.message}</p>
                            )}
                        </div>

                        <div>
                            <Label htmlFor="end_date">Tarikh Tamat *</Label>
                            <Input
                                id="end_date"
                                type="date"
                                {...register('end_date')}
                            />
                            {errors.end_date && (
                                <p className="text-sm text-red-600">{errors.end_date.message}</p>
                            )}
                        </div>

                        <div>
                            <Label htmlFor="main_venue">Venue Utama</Label>
                            <Input
                                id="main_venue"
                                {...register('main_venue')}
                                placeholder="Contoh: Stadium PU Sendayan"
                            />
                        </div>
                    </CardContent>
                </Card>
            </div>

            {/* Action Buttons */}
            <div className="flex justify-end gap-4 pt-6 border-t">
                <Button type="button" variant="outline" onClick={onCancel}>
                    Batal
                </Button>
                <Button type="submit" disabled={isLoading}>
                    {isLoading ? 'Menyimpan...' : schedule ? 'Kemaskini' : 'Tambah'} Jadual
                </Button>
            </div>
        </form>
    );
}
