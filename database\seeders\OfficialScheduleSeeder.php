<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\SportMatch;
use App\Models\SportsSchedule;
use App\Models\Sport;
use Carbon\Carbon;

class OfficialScheduleSeeder extends Seeder
{
    use BadmintonPingPongSeederMethods;
    /**
     * Run the database seeds.
     * 
     * Seeder berdasarkan JADUAL PENUH PERLAWANAN SUKAN INTRA KOR KKD 2025
     * Data dari 5 pages jadual rasmi yang telah dianalisis lengkap
     */
    public function run(): void
    {
        // Get sports and schedules for reference
        $sports = Sport::whereIn('name', [
            '<PERSON><PERSON>-<PERSON><PERSON>-g<PERSON>',
            '<PERSON><PERSON>',
            '<PERSON><PERSON>',
            '<PERSON><PERSON>',
            '<PERSON>minton',
            'Ping Pong'
        ])->get()->keyBy('name');

        $schedules = SportsSchedule::with('sport')->get()->keyBy('schedule_name');

        // Clear existing matches to avoid duplicates
        SportMatch::whereIn('sport_id', $sports->pluck('id'))->delete();

        // 1. <PERSON><PERSON>-<PERSON><PERSON>-<PERSON> 10 x 400 meter (Group Event)
        $this->seedLariLari($sports, $schedules);

        // 2. Bola Sepak (Round Robin + Final)
        $this->seedBolaSepak($sports, $schedules);

        // 3. Bola Jaring (Round Robin + Final with BAKAT)
        $this->seedBolaJaring($sports, $schedules);

        // 4. Bola Tampar (Round Robin + Final - Lelaki & Perempuan)
        $this->seedBolaTampar($sports, $schedules);

        // 5. Badminton Zon (Category Round Robin)
        $this->seedBadmintonZon($sports, $schedules);

        // 6. Badminton Serikandi (Knockout + Category)
        $this->seedBadmintonSerikandi($sports, $schedules);

        // 7. Ping Pong (Category Round Robin)
        $this->seedPingPong($sports, $schedules);
    }

    /**
     * 1. Lari-Lari Berganti-ganti 10 x 400 meter
     * Event Type: group_event
     * Date: 21 Jul 2025 (Isnin)
     * Venue: Stadium PU Sendayan
     */
    private function seedLariLari($sports, $schedules)
    {
        $sport = $sports->get('Lari-Lari Berganti-ganti');
        $schedule = $schedules->get('Acara: Lari-Lari Berganti-ganti 10 x 400 meter');

        if ($sport && $schedule) {
            SportMatch::create([
                'sport_id' => $sport->id,
                'sports_schedule_id' => $schedule->id,
                'bil' => 1,
                'title' => 'Lari-Lari Berganti-ganti 10 x 400 meter',
                'event_type' => 'group_event',
                'participating_zones' => ['1', '2', '3'], // All 3 zones compete together
                'match_date' => '2025-07-21',
                'match_time' => '21:00',
                'end_time' => '21:30',
                'venue' => 'Stadium PU Sendayan',
                'gender_type' => 'Mixed',
                'status' => 'scheduled',
                'created_by' => 1,
            ]);
        }
    }

    /**
     * 2. Bola Sepak
     * Event Type: round_robin_final
     * Dates: 21-23 Jul 2025 (3 days)
     * Venue: Stadium PU Sendayan
     */
    private function seedBolaSepak($sports, $schedules)
    {
        $sport = $sports->get('Bola Sepak');
        $schedule = $schedules->get('Acara: Bola Sepak');

        if ($sport && $schedule) {
            $matches = [
                // Round Robin Matches
                [
                    'bil' => 1,
                    'title' => 'Bola Sepak - Zon 1 vs Zon 2',
                    'match_type' => 'Round Robin',
                    'zone_a' => '1',
                    'zone_b' => '2',
                    'match_date' => '2025-07-21',
                    'match_time' => '15:00',
                    'end_time' => '16:30',
                ],
                [
                    'bil' => 2,
                    'title' => 'Bola Sepak - Zon 2 vs Zon 3',
                    'match_type' => 'Round Robin',
                    'zone_a' => '2',
                    'zone_b' => '3',
                    'match_date' => '2025-07-22',
                    'match_time' => '15:00',
                    'end_time' => '16:30',
                ],
                [
                    'bil' => 3,
                    'title' => 'Bola Sepak - Zon 1 vs Zon 3',
                    'match_type' => 'Round Robin',
                    'zone_a' => '1',
                    'zone_b' => '3',
                    'match_date' => '2025-07-23',
                    'match_time' => '15:00',
                    'end_time' => '16:30',
                ],
                // Final Match
                [
                    'bil' => 4,
                    'title' => 'Final Bola Sepak',
                    'match_type' => 'Final',
                    'zone_a' => 'Zon X',
                    'zone_b' => 'Zon Y',
                    'match_date' => '2025-07-23',
                    'match_time' => '17:00',
                    'end_time' => '18:30',
                    'is_highlighted' => true,
                    'highlight_color' => 'kuning',
                    'bracket_round' => 'Final',
                    'bracket_position' => 1,
                ],
            ];

            foreach ($matches as $matchData) {
                SportMatch::create(array_merge($matchData, [
                    'sport_id' => $sport->id,
                    'sports_schedule_id' => $schedule->id,
                    'event_type' => 'round_robin_final',
                    'venue' => 'Stadium PU Sendayan',
                    'gender_type' => 'Lelaki',
                    'status' => 'scheduled',
                    'created_by' => 1,
                ]));
            }
        }
    }

    /**
     * 3. Bola Jaring (Perempuan)
     * Event Type: round_robin_final
     * Date: 22 Jul 2025 (Selasa)
     * Venue: Kompleks Sukan PU Sendayan
     * Participants: 4 teams (Zon 1, 2, 3 + BAKAT)
     */
    private function seedBolaJaring($sports, $schedules)
    {
        $sport = $sports->get('Bola Jaring');
        $schedule = $schedules->get('Acara: Bola Jaring (Perempuan)');

        if ($sport && $schedule) {
            $matches = [
                // Round Robin Matches (6 matches for 4 teams)
                [
                    'bil' => 1,
                    'title' => 'Bola Jaring - Zon 1 vs Zon 2',
                    'match_type' => 'Round Robin',
                    'zone_a' => '1',
                    'zone_b' => '2',
                    'match_date' => '2025-07-22',
                    'match_time' => '08:00',
                    'end_time' => '09:00',
                ],
                [
                    'bil' => 2,
                    'title' => 'Bola Jaring - Zon 3 vs BAKAT',
                    'match_type' => 'Round Robin',
                    'zone_a' => '3',
                    'zone_b' => 'BAKAT',
                    'match_date' => '2025-07-22',
                    'match_time' => '09:15',
                    'end_time' => '10:15',
                ],
                [
                    'bil' => 3,
                    'title' => 'Bola Jaring - Zon 1 vs Zon 3',
                    'match_type' => 'Round Robin',
                    'zone_a' => '1',
                    'zone_b' => '3',
                    'match_date' => '2025-07-22',
                    'match_time' => '10:30',
                    'end_time' => '11:30',
                ],
                [
                    'bil' => 4,
                    'title' => 'Bola Jaring - Zon 2 vs BAKAT',
                    'match_type' => 'Round Robin',
                    'zone_a' => '2',
                    'zone_b' => 'BAKAT',
                    'match_date' => '2025-07-22',
                    'match_time' => '11:45',
                    'end_time' => '12:45',
                ],
                [
                    'bil' => 5,
                    'title' => 'Bola Jaring - Zon 1 vs BAKAT',
                    'match_type' => 'Round Robin',
                    'zone_a' => '1',
                    'zone_b' => 'BAKAT',
                    'match_date' => '2025-07-22',
                    'match_time' => '14:00',
                    'end_time' => '15:00',
                ],
                [
                    'bil' => 6,
                    'title' => 'Bola Jaring - Zon 2 vs Zon 3',
                    'match_type' => 'Round Robin',
                    'zone_a' => '2',
                    'zone_b' => '3',
                    'match_date' => '2025-07-22',
                    'match_time' => '15:15',
                    'end_time' => '16:15',
                ],
                // Final Match
                [
                    'bil' => 7,
                    'title' => 'Final Bola Jaring',
                    'match_type' => 'Final',
                    'zone_a' => 'Zon Y',
                    'zone_b' => 'Zon Z',
                    'match_date' => '2025-07-22',
                    'match_time' => '16:15',
                    'end_time' => '17:00',
                    'is_highlighted' => true,
                    'highlight_color' => 'kuning',
                    'bracket_round' => 'Final',
                    'bracket_position' => 1,
                ],
            ];

            foreach ($matches as $matchData) {
                SportMatch::create(array_merge($matchData, [
                    'sport_id' => $sport->id,
                    'sports_schedule_id' => $schedule->id,
                    'event_type' => 'round_robin_final',
                    'venue' => 'Kompleks Sukan PU Sendayan',
                    'gender_type' => 'Perempuan',
                    'status' => 'scheduled',
                    'created_by' => 1,
                ]));
            }
        }
    }

    /**
     * 4. Bola Tampar (Lelaki & Perempuan)
     * Event Type: round_robin_final
     * Date: 24 Jul 2025 (Khamis)
     * Venue: Kompleks Sukan PU Sendayan
     * Gender-specific tournaments: 2 separate tournaments
     */
    private function seedBolaTampar($sports, $schedules)
    {
        $sport = $sports->get('Bola Tampar');
        $schedule = $schedules->get('Acara: Bola Tampar (Lelaki & Perempuan)');

        if ($sport && $schedule) {
            // Bola Tampar Lelaki
            $matchesLelaki = [
                [
                    'bil' => 1,
                    'title' => 'Bola Tampar (L) - Zon 1 vs Zon 2',
                    'match_type' => 'Round Robin',
                    'zone_a' => '1',
                    'zone_b' => '2',
                    'match_date' => '2025-07-24',
                    'match_time' => '08:00',
                    'end_time' => '09:00',
                    'gender_type' => 'Lelaki',
                ],
                [
                    'bil' => 2,
                    'title' => 'Bola Tampar (L) - Zon 2 vs Zon 3',
                    'match_type' => 'Round Robin',
                    'zone_a' => '2',
                    'zone_b' => '3',
                    'match_date' => '2025-07-24',
                    'match_time' => '09:15',
                    'end_time' => '10:15',
                    'gender_type' => 'Lelaki',
                ],
                [
                    'bil' => 3,
                    'title' => 'Bola Tampar (L) - Zon 1 vs Zon 3',
                    'match_type' => 'Round Robin',
                    'zone_a' => '1',
                    'zone_b' => '3',
                    'match_date' => '2025-07-24',
                    'match_time' => '10:30',
                    'end_time' => '11:30',
                    'gender_type' => 'Lelaki',
                ],
                [
                    'bil' => 4,
                    'title' => 'Final Bola Tampar (L)',
                    'match_type' => 'Final',
                    'zone_a' => 'Zon X',
                    'zone_b' => 'Zon Y',
                    'match_date' => '2025-07-24',
                    'match_time' => '11:45',
                    'end_time' => '12:45',
                    'gender_type' => 'Lelaki',
                    'is_highlighted' => true,
                    'highlight_color' => 'kuning',
                    'bracket_round' => 'Final',
                    'bracket_position' => 1,
                ],
            ];

            // Bola Tampar Perempuan
            $matchesPerempuan = [
                [
                    'bil' => 5,
                    'title' => 'Bola Tampar (P) - Zon 1 vs Zon 2',
                    'match_type' => 'Round Robin',
                    'zone_a' => '1',
                    'zone_b' => '2',
                    'match_date' => '2025-07-24',
                    'match_time' => '14:00',
                    'end_time' => '15:00',
                    'gender_type' => 'Perempuan',
                ],
                [
                    'bil' => 6,
                    'title' => 'Bola Tampar (P) - Zon 2 vs Zon 3',
                    'match_type' => 'Round Robin',
                    'zone_a' => '2',
                    'zone_b' => '3',
                    'match_date' => '2025-07-24',
                    'match_time' => '15:15',
                    'end_time' => '16:15',
                    'gender_type' => 'Perempuan',
                ],
                [
                    'bil' => 7,
                    'title' => 'Bola Tampar (P) - Zon 1 vs Zon 3',
                    'match_type' => 'Round Robin',
                    'zone_a' => '1',
                    'zone_b' => '3',
                    'match_date' => '2025-07-24',
                    'match_time' => '16:30',
                    'end_time' => '17:30',
                    'gender_type' => 'Perempuan',
                ],
                [
                    'bil' => 8,
                    'title' => 'Final Bola Tampar (P)',
                    'match_type' => 'Final',
                    'zone_a' => 'Zon X',
                    'zone_b' => 'Zon Y',
                    'match_date' => '2025-07-24',
                    'match_time' => '17:45',
                    'end_time' => '18:45',
                    'gender_type' => 'Perempuan',
                    'is_highlighted' => true,
                    'highlight_color' => 'kuning',
                    'bracket_round' => 'Final',
                    'bracket_position' => 2,
                ],
            ];

            // Create all matches
            $allMatches = array_merge($matchesLelaki, $matchesPerempuan);
            foreach ($allMatches as $matchData) {
                SportMatch::create(array_merge($matchData, [
                    'sport_id' => $sport->id,
                    'sports_schedule_id' => $schedule->id,
                    'event_type' => 'round_robin_final',
                    'venue' => 'Kompleks Sukan PU Sendayan',
                    'status' => 'scheduled',
                    'created_by' => 1,
                ]));
            }
        }
    }
}
