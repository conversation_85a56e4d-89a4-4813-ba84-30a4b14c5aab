<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Gallery;
use App\Models\User;
use Illuminate\Http\Request;

class GalleryController extends Controller
{
    /**
     * Display a listing of gallery items
     */
    public function index(Request $request)
    {
        $query = Gallery::query();

        // Filter by category if specified
        if ($request->has('category')) {
            $query->where('category', $request->category);
        }

        // Filter by zone if specified
        if ($request->has('zone')) {
            $query->where('zone', $request->zone);
        }
        
        // Filter by file type
        if ($request->has('type')) {
            $query->where('file_type', $request->type);
        }
        
        // Search by title or description
        if ($request->has('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }
        
        // Filter by featured
        if ($request->has('featured')) {
            $query->where('is_featured', $request->boolean('featured'));
        }
        
        // Pagination
        $perPage = $request->get('per_page', 12);
        $gallery = $query->orderBy('created_at', 'desc')
                        ->paginate($perPage);
        
        // Transform the data
        $gallery->getCollection()->transform(function ($item) {
            $uploader = User::find($item->uploaded_by);

            return [
                'id' => $item->id,
                'title' => $item->title,
                'description' => $item->description,
                'file_url' => $item->main_image, // Use accessor for main image
                'file_urls' => $item->all_images, // Use accessor for all images
                'image_count' => $item->image_count,
                'file_type' => $item->file_type,
                'category' => $item->category,
                'zone' => $item->zone,
                'is_featured' => $item->is_featured,
                'views' => $item->views,
                'uploaded_at' => $item->created_at->format('Y-m-d H:i:s'),
                'uploader' => $uploader ? $uploader->name : null,
            ];
        });

        return response()->json($gallery);
    }

    /**
     * Store a new gallery item
     */
    public function store(Request $request)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'nullable|string|max:1000',
            'file_urls' => 'required|array|min:1|max:5',
            'file_urls.*' => 'required|string|max:500',
            'file_type' => 'required|in:image,video',
            'category' => 'nullable|string|max:100',
            'zone' => 'nullable|string|max:20', // Allow null for non-zone users
        ]);

        // Get authenticated user
        $user = auth()->user();
        $userId = $user->id;

        // Determine zone based on user role
        $zone = $request->zone;
        if ($user->role === 'zone') {
            // Zone users can only upload to their own zone
            $zone = $user->zone;
        } elseif (!$zone) {
            // Other roles must specify a zone
            return response()->json([
                'success' => false,
                'message' => 'Zone is required for upload'
            ], 422);
        }

        // Handle multiple images
        $fileUrls = $request->file_urls;
        $gallery = Gallery::create([
            'title' => $request->title,
            'description' => $request->description,
            'file_url' => $fileUrls[0], // Keep first image for backward compatibility
            'file_urls' => $fileUrls,
            'image_count' => count($fileUrls),
            'file_type' => $request->file_type,
            'category' => $request->category,
            'zone' => $zone, // Use determined zone
            'uploaded_by' => $userId,
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Gallery item created successfully',
            'data' => [
                'id' => $gallery->id,
                'title' => $gallery->title,
                'description' => $gallery->description,
                'file_url' => $gallery->main_image,
                'file_urls' => $gallery->all_images,
                'image_count' => $gallery->image_count,
                'file_type' => $gallery->file_type,
                'category' => $gallery->category,
                'zone' => $gallery->zone,
                'views' => $gallery->views,
                'is_featured' => $gallery->is_featured,
                'uploaded_at' => $gallery->created_at->format('Y-m-d H:i:s'),
            ]
        ], 201);
    }

    /**
     * Display the specified gallery item
     */
    public function show($id)
    {
        $item = Gallery::with('uploader')->findOrFail($id);

        // Increment views count
        $item->increment('views');

        return response()->json([
            'id' => $item->id,
            'title' => $item->title,
            'description' => $item->description,
            'file_url' => $item->main_image,
            'file_urls' => $item->all_images,
            'image_count' => $item->image_count,
            'file_type' => $item->file_type,
            'category' => $item->category,
            'zone' => $item->zone,
            'is_featured' => $item->is_featured,
            'views' => $item->views,
            'uploaded_at' => $item->created_at->format('Y-m-d H:i:s'),
            'uploader' => $item->uploader ? $item->uploader->name : null,
        ]);
    }

    /**
     * Store multiple images for gallery
     */
    public function storeMultiple(Request $request)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'nullable|string|max:1000',
            'images' => 'required|array|min:1|max:5',
            'images.*' => 'required|image|mimes:jpeg,png,jpg,gif|max:10240', // 10MB max per image
            'category' => 'nullable|string|max:100',
            'zone' => 'nullable|string|max:20',
        ]);

        // Get authenticated user
        $user = auth()->user();
        $userId = $user->id;

        // Determine zone based on user role
        $zone = $request->zone;
        if ($user->role === 'zone') {
            $zone = $user->zone;
        } elseif (!$zone) {
            return response()->json([
                'success' => false,
                'message' => 'Zone is required for upload'
            ], 422);
        }

        $uploadedUrls = [];

        // Upload each image
        foreach ($request->file('images') as $image) {
            // Here you would implement your image upload logic
            // For now, we'll assume you have an upload service
            $uploadedUrls[] = $this->uploadImage($image);
        }

        // Create gallery item with multiple images
        $gallery = Gallery::create([
            'title' => $request->title,
            'description' => $request->description,
            'file_url' => $uploadedUrls[0], // First image for backward compatibility
            'file_urls' => $uploadedUrls,
            'image_count' => count($uploadedUrls),
            'file_type' => 'image',
            'category' => $request->category,
            'zone' => $zone,
            'uploaded_by' => $userId,
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Gallery item with multiple images created successfully',
            'data' => [
                'id' => $gallery->id,
                'title' => $gallery->title,
                'description' => $gallery->description,
                'file_url' => $gallery->main_image,
                'file_urls' => $gallery->all_images,
                'image_count' => $gallery->image_count,
                'file_type' => $gallery->file_type,
                'category' => $gallery->category,
                'zone' => $gallery->zone,
                'views' => $gallery->views,
                'uploaded_at' => $gallery->created_at->format('Y-m-d H:i:s'),
            ]
        ], 201);
    }

    /**
     * Helper method to upload image (placeholder)
     */
    private function uploadImage($image)
    {
        // This should integrate with your existing image upload service
        // For now, return a placeholder
        return '/storage/gallery/' . time() . '_' . $image->getClientOriginalName();
    }

    /**
     * Get featured gallery items
     */
    public function featured(Request $request)
    {
        $limit = $request->get('limit', 8);

        $gallery = Gallery::with('uploader')
                         ->where('is_featured', true)
                         ->orderBy('created_at', 'desc')
                         ->limit($limit)
                         ->get();

        $gallery = $gallery->map(function ($item) {
            return [
                'id' => $item->id,
                'title' => $item->title,
                'description' => $item->description,
                'file_url' => $item->file_url,
                'file_type' => $item->file_type,
                'category' => $item->category,
                'zone' => $item->zone,
                'uploaded_at' => $item->created_at->format('Y-m-d H:i:s'),
            ];
        });

        return response()->json($gallery);
    }

    /**
     * Get gallery categories
     */
    public function categories()
    {
        $categories = Gallery::distinct()
                            ->pluck('category')
                            ->filter()
                            ->values();

        return response()->json($categories);
    }

    /**
     * Get gallery items by sport
     */
    public function bySport(Request $request)
    {
        $sports = Sport::with(['galleryItems' => function ($query) use ($request) {
            $query->orderBy('created_at', 'desc');
            if ($request->has('limit')) {
                $query->limit($request->limit);
            }
        }])->get();

        $result = $sports->map(function ($sport) {
            return [
                'sport_id' => $sport->id,
                'sport_name' => $sport->name,
                'items' => $sport->galleryItems->map(function ($item) {
                    return [
                        'id' => $item->id,
                        'title' => $item->title,
                        'file_path' => $item->file_path,
                        'file_type' => $item->file_type,
                        'category' => $item->category,
                        'uploaded_at' => $item->created_at->format('Y-m-d H:i:s'),
                    ];
                })
            ];
        });

        return response()->json($result);
    }
}
