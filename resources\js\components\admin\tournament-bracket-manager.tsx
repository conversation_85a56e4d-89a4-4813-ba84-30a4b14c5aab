
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Trophy } from 'lucide-react';
import ModernTournamentBracket from './modern-tournament-bracket';

interface TournamentBracketManagerProps {
    onMatchSelect?: (match: any) => void;
}

export default function TournamentBracketManager({ onMatchSelect }: TournamentBracketManagerProps) {




    return (
        <div className="space-y-6">
            {/* Header */}
            <Card>
                <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                        <Trophy className="h-6 w-6 text-yellow-600" />
                        Tournament Bracket Manager
                    </CardTitle>
                    <CardDescription>
                        Urus dan papar bracket tournament untuk setiap sukan
                    </CardDescription>
                </CardHeader>
                <CardContent>
                    <div className="text-center text-gray-600">
                        <p>Gunakan Modern Tournament Bracket di bawah untuk melihat dan mengurus bracket tournament.</p>
                    </div>
                </CardContent>
            </Card>

            {/* Modern Tournament Bracket */}
            <div className="space-y-6">
                <ModernTournamentBracket onMatchSelect={onMatchSelect} />
            </div>
        </div>
    );
};
