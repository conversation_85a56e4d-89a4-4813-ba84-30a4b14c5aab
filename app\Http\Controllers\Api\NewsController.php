<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\News;
use Illuminate\Http\Request;
use Illuminate\Support\Str;

class NewsController extends Controller
{
    /**
     * Display a listing of news articles
     */
    public function index(Request $request)
    {
        $query = News::query();

        // Filter by status (only published for public API)
        $query->where('is_published', true);
        
        // Filter by category if specified
        if ($request->has('category')) {
            $query->where('category', $request->category);
        }
        
        // Search by title or content
        if ($request->has('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                  ->orWhere('excerpt', 'like', "%{$search}%")
                  ->orWhere('content', 'like', "%{$search}%");
            });
        }
        
        // Filter by important
        if ($request->has('important')) {
            $query->where('is_important', $request->boolean('important'));
        }
        
        // Pagination
        $perPage = $request->get('per_page', 10);
        $news = $query->orderBy('published_at', 'desc')
                     ->paginate($perPage);
        
        // Transform the data
        $news->getCollection()->transform(function ($article) {
            return [
                'id' => $article->id,
                'title' => $article->title,
                'slug' => $article->slug,
                'excerpt' => $article->excerpt,
                'featured_image' => $article->featured_image,
                'category' => $article->category,
                'tags' => json_decode($article->tags, true),
                'is_important' => $article->is_important,
                'views' => $article->views,
                'published_at' => $article->published_at->format('Y-m-d H:i:s'),
                'author' => $article->author,
            ];
        });

        return response()->json($news);
    }

    /**
     * Display the specified news article
     */
    public function show($id)
    {
        $article = News::where('is_published', true)
                      ->findOrFail($id);

        return response()->json([
            'id' => $article->id,
            'title' => $article->title,
            'slug' => $article->slug,
            'excerpt' => $article->excerpt,
            'content' => $article->content,
            'featured_image' => $article->featured_image,
            'category' => $article->category,
            'tags' => json_decode($article->tags, true),
            'is_important' => $article->is_important,
            'views' => $article->views,
            'published_at' => $article->published_at->format('Y-m-d H:i:s'),
            'author' => $article->author,
            'zone' => $article->zone,
        ]);
    }

    /**
     * Increment view count for news article
     */
    public function incrementView($id)
    {
        $article = News::where('is_published', true)->findOrFail($id);
        $article->increment('views');

        return response()->json([
            'success' => true,
            'views' => $article->views
        ]);
    }

    /**
     * Admin index - get all news for management
     */
    public function adminIndex()
    {
        $news = News::orderBy('created_at', 'desc')->paginate(10);

        return response()->json([
            'data' => $news->items(),
            'pagination' => [
                'current_page' => $news->currentPage(),
                'last_page' => $news->lastPage(),
                'per_page' => $news->perPage(),
                'total' => $news->total(),
            ]
        ]);
    }

    /**
     * Admin show - get single news for editing
     */
    public function adminShow($id)
    {
        $article = News::findOrFail($id);

        return response()->json([
            'id' => $article->id,
            'title' => $article->title,
            'slug' => $article->slug,
            'excerpt' => $article->excerpt,
            'content' => $article->content,
            'featured_image' => $article->featured_image,
            'category' => $article->category,
            'tags' => json_decode($article->tags, true),
            'is_important' => $article->is_important,
            'is_published' => $article->is_published,
            'views' => $article->views,
            'published_at' => $article->published_at ? $article->published_at->format('Y-m-d H:i:s') : null,
            'author' => $article->author,
            'zone' => $article->zone,
            'created_at' => $article->created_at->format('Y-m-d H:i:s'),
            'updated_at' => $article->updated_at->format('Y-m-d H:i:s'),
        ]);
    }

    /**
     * Store a new news article
     */
    public function store(Request $request)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'excerpt' => 'required|string|max:500',
            'content' => 'required|string',
            'category' => 'required|in:announcement,registration,schedule,results,tips,general',
            'tags' => 'nullable|string',
            'is_important' => 'boolean',
            'featured_image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:10240'
        ]);

        $data = $request->all();
        $data['slug'] = Str::slug($request->title);
        $data['author'] = auth()->user()->name;
        $data['zone'] = auth()->user()->zone ?? 'all';
        $data['is_published'] = true;
        $data['published_at'] = now();
        $data['views'] = 0;

        // Handle tags
        if ($request->tags) {
            $tags = is_string($request->tags) ? json_decode($request->tags, true) : $request->tags;
            $data['tags'] = json_encode($tags);
        }

        // Handle image upload
        if ($request->hasFile('featured_image')) {
            $image = $request->file('featured_image');
            $filename = time() . '_' . Str::random(10) . '.' . $image->getClientOriginalExtension();
            $path = $image->storeAs('news', $filename, 'public');
            $data['featured_image'] = '/storage/' . $path;
        }

        $article = News::create($data);

        return response()->json([
            'message' => 'News article created successfully',
            'data' => $article
        ], 201);
    }

    /**
     * Update news article
     */
    public function update(Request $request, $id)
    {
        $article = News::findOrFail($id);

        $request->validate([
            'title' => 'required|string|max:255',
            'excerpt' => 'required|string|max:500',
            'content' => 'required|string',
            'category' => 'required|in:announcement,registration,schedule,results,tips,general',
            'tags' => 'nullable|string',
            'is_important' => 'boolean',
            'featured_image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:10240'
        ]);

        $data = $request->all();
        $data['slug'] = Str::slug($request->title);

        // Handle tags
        if ($request->tags) {
            $tags = is_string($request->tags) ? json_decode($request->tags, true) : $request->tags;
            $data['tags'] = json_encode($tags);
        }

        // Handle image upload
        if ($request->hasFile('featured_image')) {
            // Delete old image if exists
            if ($article->featured_image && file_exists(public_path($article->featured_image))) {
                unlink(public_path($article->featured_image));
            }

            $image = $request->file('featured_image');
            $filename = time() . '_' . Str::random(10) . '.' . $image->getClientOriginalExtension();
            $path = $image->storeAs('news', $filename, 'public');
            $data['featured_image'] = '/storage/' . $path;
        }

        $article->update($data);

        return response()->json([
            'message' => 'News article updated successfully',
            'data' => $article
        ]);
    }

    /**
     * Delete news article
     */
    public function destroy($id)
    {
        $article = News::findOrFail($id);

        // Delete image if exists
        if ($article->featured_image && file_exists(public_path($article->featured_image))) {
            unlink(public_path($article->featured_image));
        }

        $article->delete();

        return response()->json([
            'message' => 'News article deleted successfully'
        ]);
    }

    /**
     * Get featured news articles
     */
    public function featured(Request $request)
    {
        $limit = $request->get('limit', 5);

        $news = News::where('is_published', true)
                   ->where('is_important', true)
                   ->orderBy('published_at', 'desc')
                   ->limit($limit)
                   ->get();

        $news = $news->map(function ($article) {
            return [
                'id' => $article->id,
                'title' => $article->title,
                'slug' => $article->slug,
                'excerpt' => $article->excerpt,
                'featured_image' => $article->featured_image,
                'category' => $article->category,
                'published_at' => $article->published_at->format('Y-m-d H:i:s'),
                'author' => $article->author,
            ];
        });

        return response()->json($news);
    }

    /**
     * Get news categories
     */
    public function categories()
    {
        $categories = News::where('is_published', true)
                         ->distinct()
                         ->pluck('category')
                         ->filter()
                         ->values();

        return response()->json($categories);
    }
}
