import { useState, useEffect } from 'react';
import publicApi from '@/lib/public-api';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from '@/components/ui/table';
import {
    Trophy,
    Calendar,
    Clock,
    MapPin,
    Users,
    Search,
    Filter,
    Eye
} from 'lucide-react';

interface PublicSportMatch {
    id: number;
    sport_id: number;
    bil: number;
    title: string;
    match_type: string;
    zone_a: string;
    zone_b: string;
    special_teams: string;
    match_date: string;
    match_time: string;
    end_time: string;
    venue: string;
    court_field: string;
    category: string;
    gender_type: string;
    status: 'scheduled' | 'ongoing' | 'completed' | 'cancelled';
    is_highlighted: boolean;
    highlight_color: string;
    bracket_round: string;
    score_zone_a?: number;
    score_zone_b?: number;
    winner_zone?: string;
    sport: {
        id: number;
        name: string;
        logo_url?: string;
    };
    sports_schedule: {
        id: number;
        schedule_name: string;
    };
}

interface PublicSportsSchedule {
    id: number;
    schedule_name: string;
    description: string;
    start_date: string;
    end_date: string;
    main_venue: string;
    sport: {
        id: number;
        name: string;
        logo_url?: string;
    };
}

export default function JadualPertandinganPublic() {
    const [matches, setMatches] = useState<PublicSportMatch[]>([]);
    const [schedules, setSchedules] = useState<PublicSportsSchedule[]>([]);
    const [isLoading, setIsLoading] = useState(true);
    const [searchTerm, setSearchTerm] = useState('');
    const [statusFilter, setStatusFilter] = useState('');
    const [sportFilter, setSportFilter] = useState('');
    const [selectedSchedule, setSelectedSchedule] = useState<number | null>(null);
    const [viewMode, setViewMode] = useState<'schedule' | 'matches'>('schedule');

    useEffect(() => {
        fetchData();
    }, []);

    const fetchData = async () => {
        try {
            setIsLoading(true);
            const [matchesRes, schedulesRes] = await Promise.all([
                publicApi.get('/jadual/matches'),
                publicApi.get('/jadual/sports-schedules')
            ]);

            setMatches(matchesRes.data.data || []);
            setSchedules(schedulesRes.data.data || []);
        } catch (error) {
            console.error('Error fetching data:', error);
        } finally {
            setIsLoading(false);
        }
    };

    const getStatusBadge = (status: string) => {
        switch (status) {
            case 'scheduled':
                return <Badge className="bg-blue-100 text-blue-800">Dijadualkan</Badge>;
            case 'ongoing':
                return <Badge className="bg-yellow-100 text-yellow-800">Sedang Berlangsung</Badge>;
            case 'completed':
                return <Badge className="bg-green-100 text-green-800">Selesai</Badge>;
            case 'cancelled':
                return <Badge className="bg-red-100 text-red-800">Dibatalkan</Badge>;
            default:
                return <Badge variant="secondary">{status}</Badge>;
        }
    };

    const getCategoryBadge = (category: string, highlightColor: string) => {
        const colorClasses = {
            'kuning': 'bg-yellow-100 text-yellow-800',
            'merah jambu': 'bg-pink-100 text-pink-800',
            'hijau': 'bg-green-100 text-green-800',
            'oren': 'bg-orange-100 text-orange-800',
        };

        const colorClass = highlightColor ? colorClasses[highlightColor as keyof typeof colorClasses] : 'bg-gray-100 text-gray-800';

        return (
            <Badge className={colorClass}>
                {category}
            </Badge>
        );
    };

    const formatDateTime = (date: string, time: string, endTime?: string) => {
        const matchDate = new Date(date);
        const timeRange = endTime ? `${time} - ${endTime}` : time;
        
        return {
            date: matchDate.toLocaleDateString('ms-MY', { 
                weekday: 'long', 
                day: 'numeric',
                month: 'long',
                year: 'numeric'
            }),
            time: timeRange
        };
    };

    const filteredMatches = matches.filter(match => {
        const matchesSearch = searchTerm === '' || 
            match.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
            match.sport.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
            match.venue.toLowerCase().includes(searchTerm.toLowerCase());
        
        const matchesStatus = statusFilter === '' || match.status === statusFilter;
        const matchesSport = sportFilter === '' || match.sport_id.toString() === sportFilter;
        const matchesSchedule = selectedSchedule === null || match.sports_schedule?.id === selectedSchedule;

        return matchesSearch && matchesStatus && matchesSport && matchesSchedule;
    });

    const groupedMatches = filteredMatches.reduce((groups, match) => {
        const scheduleKey = match.sports_schedule?.schedule_name || 'Tiada Jadual';
        if (!groups[scheduleKey]) {
            groups[scheduleKey] = [];
        }
        groups[scheduleKey].push(match);
        return groups;
    }, {} as Record<string, PublicSportMatch[]>);

    const uniqueSports = Array.from(new Set(matches.map(m => m.sport_id)))
        .map(sportId => matches.find(m => m.sport_id === sportId)?.sport)
        .filter(Boolean);

    if (isLoading) {
        return (
            <div className="flex items-center justify-center h-64">
                <div className="text-lg">Loading...</div>
            </div>
        );
    }

    return (
        <div className="space-y-6">
            {/* Header */}
            <div className="text-center space-y-4">
                <h1 className="text-3xl font-bold text-gray-900">
                    Jadual Pertandingan
                </h1>
                <p className="text-gray-600 max-w-2xl mx-auto">
                    Jadual lengkap pertandingan Sukan Intra Kor Kesihatan DiRaja 2025
                </p>
            </div>

            {/* View Mode Toggle */}
            <div className="flex justify-center">
                <div className="flex bg-gray-100 rounded-lg p-1">
                    <Button
                        variant={viewMode === 'schedule' ? 'default' : 'ghost'}
                        size="sm"
                        onClick={() => setViewMode('schedule')}
                        className="flex items-center gap-2"
                    >
                        <Calendar className="h-4 w-4" />
                        Jadual Sukan
                    </Button>
                    <Button
                        variant={viewMode === 'matches' ? 'default' : 'ghost'}
                        size="sm"
                        onClick={() => setViewMode('matches')}
                        className="flex items-center gap-2"
                    >
                        <Trophy className="h-4 w-4" />
                        Senarai Perlawanan
                    </Button>
                </div>
            </div>

            {viewMode === 'schedule' ? (
                /* Schedule Cards View */
                <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-2 xl:grid-cols-3">
                    {schedules.map((schedule) => {
                        const scheduleMatches = matches.filter(m => m.sports_schedule?.id === schedule.id);
                        const upcomingMatches = scheduleMatches.filter(m => m.status === 'scheduled');
                        const completedMatches = scheduleMatches.filter(m => m.status === 'completed');

                        return (
                            <Card key={schedule.id} className="hover:shadow-lg transition-shadow">
                                <CardHeader>
                                    <div className="flex items-center gap-3">
                                        {schedule.sport.logo_url && (
                                            <img 
                                                src={schedule.sport.logo_url} 
                                                alt={schedule.sport.name}
                                                className="w-12 h-12 object-contain"
                                            />
                                        )}
                                        <div>
                                            <CardTitle className="text-lg">{schedule.sport.name}</CardTitle>
                                            <CardDescription className="text-sm">
                                                {schedule.schedule_name}
                                            </CardDescription>
                                        </div>
                                    </div>
                                </CardHeader>
                                <CardContent className="space-y-4">
                                    <div className="text-sm text-gray-600">
                                        <div className="flex items-center gap-2 mb-2">
                                            <Calendar className="h-4 w-4" />
                                            <span>
                                                {new Date(schedule.start_date).toLocaleDateString('ms-MY')} - 
                                                {new Date(schedule.end_date).toLocaleDateString('ms-MY')}
                                            </span>
                                        </div>
                                        {schedule.main_venue && (
                                            <div className="flex items-center gap-2">
                                                <MapPin className="h-4 w-4" />
                                                <span>{schedule.main_venue}</span>
                                            </div>
                                        )}
                                    </div>

                                    <div className="grid grid-cols-2 gap-4 text-center">
                                        <div>
                                            <div className="text-2xl font-bold text-blue-600">{upcomingMatches.length}</div>
                                            <div className="text-xs text-gray-600">Akan Datang</div>
                                        </div>
                                        <div>
                                            <div className="text-2xl font-bold text-green-600">{completedMatches.length}</div>
                                            <div className="text-xs text-gray-600">Selesai</div>
                                        </div>
                                    </div>

                                    <Button 
                                        variant="outline" 
                                        size="sm" 
                                        className="w-full"
                                        onClick={() => {
                                            setSelectedSchedule(schedule.id);
                                            setViewMode('matches');
                                        }}
                                    >
                                        <Eye className="h-4 w-4 mr-2" />
                                        Lihat Perlawanan
                                    </Button>
                                </CardContent>
                            </Card>
                        );
                    })}
                </div>
            ) : (
                /* Matches List View */
                <div className="space-y-6">
                    {/* Filters */}
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <Filter className="h-5 w-5" />
                                Penapis & Carian
                            </CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="grid gap-4 md:grid-cols-4">
                                <div className="relative">
                                    <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                                    <Input
                                        placeholder="Cari perlawanan..."
                                        value={searchTerm}
                                        onChange={(e) => setSearchTerm(e.target.value)}
                                        className="pl-10"
                                    />
                                </div>
                                
                                <Select value={sportFilter} onValueChange={setSportFilter}>
                                    <SelectTrigger>
                                        <SelectValue placeholder="Pilih Sukan" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem value="">Semua Sukan</SelectItem>
                                        {uniqueSports.map((sport) => (
                                            <SelectItem key={sport!.id} value={sport!.id.toString()}>
                                                {sport!.name}
                                            </SelectItem>
                                        ))}
                                    </SelectContent>
                                </Select>

                                <Select value={selectedSchedule?.toString() || ''} onValueChange={(value) => setSelectedSchedule(value ? parseInt(value) : null)}>
                                    <SelectTrigger>
                                        <SelectValue placeholder="Pilih Jadual" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem value="">Semua Jadual</SelectItem>
                                        {schedules.map((schedule) => (
                                            <SelectItem key={schedule.id} value={schedule.id.toString()}>
                                                {schedule.schedule_name}
                                            </SelectItem>
                                        ))}
                                    </SelectContent>
                                </Select>

                                <Select value={statusFilter} onValueChange={setStatusFilter}>
                                    <SelectTrigger>
                                        <SelectValue placeholder="Status" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem value="">Semua Status</SelectItem>
                                        <SelectItem value="scheduled">Dijadualkan</SelectItem>
                                        <SelectItem value="ongoing">Sedang Berlangsung</SelectItem>
                                        <SelectItem value="completed">Selesai</SelectItem>
                                    </SelectContent>
                                </Select>
                            </div>
                        </CardContent>
                    </Card>

                    {/* Grouped Matches */}
                    <div className="space-y-6">
                        {Object.entries(groupedMatches).map(([scheduleName, matches]) => (
                            <Card key={scheduleName}>
                                <CardHeader>
                                    <div className="flex items-center justify-between">
                                        <div>
                                            <CardTitle className="text-lg">{scheduleName}</CardTitle>
                                            <CardDescription>
                                                {matches.length} perlawanan • {matches[0]?.sport?.name}
                                            </CardDescription>
                                        </div>
                                        <Badge variant="outline">
                                            {matches.filter(m => m.status === 'completed').length} / {matches.length} selesai
                                        </Badge>
                                    </div>
                                </CardHeader>
                                <CardContent>
                                    <div className="overflow-x-auto">
                                        <Table>
                                            <TableHeader>
                                                <TableRow>
                                                    <TableHead className="w-16">Bil</TableHead>
                                                    <TableHead>Perlawanan</TableHead>
                                                    <TableHead>Kategori</TableHead>
                                                    <TableHead>Tarikh & Masa</TableHead>
                                                    <TableHead>Venue</TableHead>
                                                    <TableHead>Status</TableHead>
                                                    <TableHead>Keputusan</TableHead>
                                                </TableRow>
                                            </TableHeader>
                                            <TableBody>
                                                {matches
                                                    .sort((a, b) => (a.bil || 0) - (b.bil || 0))
                                                    .map((match) => {
                                                        const dateTime = formatDateTime(match.match_date, match.match_time, match.end_time);
                                                        const hasResult = match.status === 'completed' &&
                                                            match.score_zone_a !== undefined &&
                                                            match.score_zone_b !== undefined;

                                                        return (
                                                            <TableRow
                                                                key={match.id}
                                                                className={match.is_highlighted ? 'bg-yellow-50 border-yellow-200' : ''}
                                                            >
                                                                <TableCell>
                                                                    <Badge variant="outline" className="font-mono">
                                                                        {match.bil || '-'}
                                                                    </Badge>
                                                                </TableCell>
                                                                <TableCell>
                                                                    <div className="space-y-1">
                                                                        <div className="font-medium">{match.title}</div>
                                                                        <div className="flex items-center gap-2 text-sm text-gray-600">
                                                                            {match.zone_a && (
                                                                                <>
                                                                                    <span className="font-medium">{match.zone_a}</span>
                                                                                    <span className="text-gray-400">vs</span>
                                                                                    <span className="font-medium">{match.zone_b}</span>
                                                                                </>
                                                                            )}
                                                                            {match.special_teams && (
                                                                                <Badge variant="secondary" className="text-xs">
                                                                                    {match.special_teams}
                                                                                </Badge>
                                                                            )}
                                                                        </div>
                                                                        {match.match_type && (
                                                                            <Badge
                                                                                variant={match.match_type.includes('Final') ? 'default' : 'outline'}
                                                                                className="text-xs"
                                                                            >
                                                                                {match.match_type}
                                                                            </Badge>
                                                                        )}
                                                                    </div>
                                                                </TableCell>
                                                                <TableCell>
                                                                    {match.category ? (
                                                                        <div className="space-y-1">
                                                                            {getCategoryBadge(match.category, match.highlight_color)}
                                                                            {match.gender_type && (
                                                                                <div className="text-xs text-gray-500">
                                                                                    {match.gender_type}
                                                                                </div>
                                                                            )}
                                                                        </div>
                                                                    ) : (
                                                                        <span className="text-gray-400">-</span>
                                                                    )}
                                                                </TableCell>
                                                                <TableCell>
                                                                    <div className="text-sm">
                                                                        <div className="font-medium">{dateTime.date}</div>
                                                                        <div className="text-gray-500 flex items-center gap-1">
                                                                            <Clock className="h-3 w-3" />
                                                                            {dateTime.time}
                                                                        </div>
                                                                    </div>
                                                                </TableCell>
                                                                <TableCell>
                                                                    <div className="space-y-1">
                                                                        <div className="flex items-center gap-1">
                                                                            <MapPin className="h-3 w-3 text-gray-400" />
                                                                            <span className="text-sm">{match.venue}</span>
                                                                        </div>
                                                                        {match.court_field && (
                                                                            <Badge variant="outline" className="font-mono text-xs">
                                                                                {match.court_field}
                                                                            </Badge>
                                                                        )}
                                                                    </div>
                                                                </TableCell>
                                                                <TableCell>
                                                                    {getStatusBadge(match.status)}
                                                                </TableCell>
                                                                <TableCell>
                                                                    {hasResult ? (
                                                                        <div className="space-y-1">
                                                                            <div className="text-sm font-mono">
                                                                                {match.score_zone_a} - {match.score_zone_b}
                                                                            </div>
                                                                            {match.winner_zone && (
                                                                                <Badge className="bg-green-100 text-green-800 text-xs">
                                                                                    <Trophy className="h-3 w-3 mr-1" />
                                                                                    {match.winner_zone}
                                                                                </Badge>
                                                                            )}
                                                                        </div>
                                                                    ) : (
                                                                        <span className="text-gray-400 text-sm">-</span>
                                                                    )}
                                                                </TableCell>
                                                            </TableRow>
                                                        );
                                                    })}
                                            </TableBody>
                                        </Table>
                                    </div>
                                </CardContent>
                            </Card>
                        ))}
                    </div>

                    {/* Empty State */}
                    {Object.keys(groupedMatches).length === 0 && (
                        <Card>
                            <CardContent className="flex flex-col items-center justify-center py-12">
                                <Trophy className="h-12 w-12 text-gray-400 mb-4" />
                                <h3 className="text-lg font-medium text-gray-900 mb-2">
                                    Tiada Perlawanan Dijumpai
                                </h3>
                                <p className="text-gray-600 text-center mb-4">
                                    Tiada perlawanan yang sepadan dengan kriteria carian anda.
                                </p>
                                <Button
                                    variant="outline"
                                    onClick={() => {
                                        setSearchTerm('');
                                        setSportFilter('');
                                        setStatusFilter('');
                                        setSelectedSchedule(null);
                                    }}
                                >
                                    Reset Penapis
                                </Button>
                            </CardContent>
                        </Card>
                    )}
                </div>
            )}
        </div>
    );
}
