import { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import api from '@/lib/axios';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { toast } from 'react-hot-toast';
import HeadToHeadScoreInput from './HeadToHeadScoreInput';
import GroupEventRankings from './GroupEventRankings';
import WalkoverSection from './WalkoverSection';
import SetBasedScoreInput from './SetBasedScoreInput';

// Helper function to format date time to Malaysia timezone
const formatMalaysiaDateTime = (dateString: string, timeString: string) => {
    try {
        const dateTime = new Date(`${dateString}T${timeString}`);
        return dateTime.toLocaleString('ms-MY', {
            timeZone: 'Asia/Kuala_Lumpur',
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            hour12: false
        });
    } catch (error) {
        return `${dateString} ${timeString}`;
    }
};

// Sport classification functions
const isGroupEventSport = (sportName: string) => {
    return ['Lari-Lari Berganti-ganti', 'Sukan Rakyat'].includes(sportName);
};

// Sport rules
const getSportScoringRules = (sportName: string) => {
    const rules = {
        'Badminton': { maxScore: 30, unit: 'mata', scoreType: 'points', eventType: 'head_to_head' },
        'Ping Pong': { maxScore: 30, unit: 'mata', scoreType: 'points', eventType: 'head_to_head' },
        'Bola Tampar': { maxScore: 30, unit: 'mata', scoreType: 'points', eventType: 'head_to_head' },
        'Bola Sepak': { maxScore: 10, unit: 'gol', scoreType: 'goals', eventType: 'head_to_head' },
        'Bola Jaring': { maxScore: 100, unit: 'mata', scoreType: 'points', eventType: 'head_to_head' },
        'Lari-Lari Berganti-ganti': { maxScore: 999, unit: 'saat', scoreType: 'time', eventType: 'group_event' },
        'Larian 10 x 400m': { maxScore: 999, unit: 'saat', scoreType: 'time', eventType: 'knockout_group' },
        'Sukan Rakyat': { maxScore: 100, unit: 'mata', scoreType: 'points', eventType: 'group_event' }
    };
    return rules[sportName as keyof typeof rules] || { maxScore: 100, unit: 'mata', scoreType: 'points', eventType: 'head_to_head' };
};

// Form schemas
const createResultSchema = (sportName: string, eventType: string) => {
    const rules = getSportScoringRules(sportName);

    if (eventType === 'group_event' || eventType === 'knockout_group') {
        return z.object({
            zone_rankings: z.array(z.object({
                zone_code: z.string(),
                final_score: z.union([z.number(), z.string()]),
                time_result: z.string().optional(),
                ranking_position: z.number().min(1),
                points_earned: z.number().min(0)
            })).min(2, 'Minimum 2 zon diperlukan'),
            notes: z.string().optional(),
            detailed_scores: z.string().optional(),
        });
    } else {
        return z.object({
            score_zone_a: z.number()
                .min(0, 'Skor tidak boleh negatif')
                .max(rules.maxScore, `Skor maksimum ${rules.maxScore} ${rules.unit}`),
            score_zone_b: z.number()
                .min(0, 'Skor tidak boleh negatif')
                .max(rules.maxScore, `Skor maksimum ${rules.maxScore} ${rules.unit}`),
            points_zone_a: z.number().min(0).optional(),
            points_zone_b: z.number().min(0).optional(),
            winner_zone: z.string().optional(),
            notes: z.string().optional(),
            detailed_scores: z.string().optional(),
        });
    }
};

interface MatchResultFormProps {
    match: any;
    onSuccess: () => void;
    onClose: () => void;
}

export default function MatchResultForm({ match, onSuccess, onClose }: MatchResultFormProps) {
    const [isLoading, setIsLoading] = useState(false);
    const [selectedWinner, setSelectedWinner] = useState<string>('');
    const [isWalkover, setIsWalkover] = useState(false);

    const sportRules = getSportScoringRules(match?.sport_name || '');
    const eventType = sportRules.eventType;
    const resultSchema = createResultSchema(match?.sport_name || '', eventType);

    const {
        register,
        handleSubmit,
        setValue,
        watch,
        reset,
        formState: { errors }
    } = useForm({
        resolver: zodResolver(resultSchema),
        defaultValues: {
            score_zone_a: Number(match?.score_zone_a) || 0,
            score_zone_b: Number(match?.score_zone_b) || 0,
            points_zone_a: Number(match?.points_zone_a) || 0,
            points_zone_b: Number(match?.points_zone_b) || 0,
            winner_zone: String(match?.winner_zone || ''),
            notes: String(match?.notes || ''),
            detailed_scores: String(match?.detailed_scores || ''),
            zone_rankings: eventType === 'group_event' || eventType === 'knockout_group'
                ? match?.participating_zones?.map((zoneCode: string) => ({
                    zone_code: zoneCode,
                    final_score: 0,
                    ranking_position: 0,
                    points_earned: 0
                })) || []
                : undefined
        }
    });

    // Debug form errors
    console.log('Form errors:', errors);

    const scoreA = watch('score_zone_a');
    const scoreB = watch('score_zone_b');

    // Auto-calculate winner and tournament points based on match scores
    useEffect(() => {
        if (!match || isWalkover) return;

        const sA = scoreA || 0;
        const sB = scoreB || 0;

        if (sA > sB) {
            setSelectedWinner(String(match.zone_a));
            setValue('winner_zone', String(match.zone_a), { shouldValidate: false });
            setValue('points_zone_a', 3, { shouldValidate: false });
            setValue('points_zone_b', 0, { shouldValidate: false });
        } else if (sB > sA) {
            setSelectedWinner(String(match.zone_b));
            setValue('winner_zone', String(match.zone_b), { shouldValidate: false });
            setValue('points_zone_a', 0, { shouldValidate: false });
            setValue('points_zone_b', 3, { shouldValidate: false });
        } else if (sA === sB && sA > 0) {
            setSelectedWinner('draw');
            setValue('winner_zone', 'draw', { shouldValidate: false });
            setValue('points_zone_a', 1, { shouldValidate: false });
            setValue('points_zone_b', 1, { shouldValidate: false });
        } else {
            setSelectedWinner('');
            setValue('winner_zone', '', { shouldValidate: false });
            setValue('points_zone_a', 0, { shouldValidate: false });
            setValue('points_zone_b', 0, { shouldValidate: false });
        }
    }, [scoreA, scoreB, match, setValue, isWalkover]);

    const onSubmit = async (data: any) => {
        console.log('=== FORM SUBMISSION DEBUG ===');
        console.log('onSubmit called with data:', data);
        console.log('Event type:', eventType);
        console.log('Match:', match);
        if (!match) {
            console.log('No match found, returning');
            return;
        }

        setIsLoading(true);
        try {
            let submitData: any = {
                ...data,
                status: 'completed'
            };

            // Handle group events and knockout group events
            if ((eventType === 'group_event' || eventType === 'knockout_group') && 'zone_rankings' in data && data.zone_rankings) {
                submitData.zone_rankings = data.zone_rankings;
                submitData.score_zone_a = 0;
                submitData.score_zone_b = 0;
                submitData.winner_zone = submitData.zone_rankings.find((r: any) => r.ranking_position === 1)?.zone_code || null;
            } else {
                // Head-to-head events
                if (!submitData.winner_zone && selectedWinner) {
                    submitData.winner_zone = selectedWinner;
                }
            }

            console.log('Submitting data:', submitData);
            await api.put(`/admin/sport-matches/${match.id}/result`, submitData);

            toast.success('Keputusan perlawanan berjaya disimpan!');
            onSuccess();
            onClose();
            reset();
        } catch (error: any) {
            console.error('Error submitting result:', error);
            toast.error(error.response?.data?.message || 'Ralat menyimpan keputusan');
        } finally {
            setIsLoading(false);
        }
    };

    if (!match) return null;

    const onError = (errors: any) => {
        console.log('=== FORM VALIDATION ERRORS ===');
        console.log('Validation errors:', errors);
    };

    return (
        <form onSubmit={handleSubmit(onSubmit, onError)} className="flex flex-col h-full">
            <div className="overflow-y-auto max-h-[calc(95vh-140px)] p-4 pb-20 space-y-6">
                
                {/* Group Event Rankings */}
                {(eventType === 'group_event' || eventType === 'knockout_group' ||
                  ['Larian 10 x 400m', 'Lari-Lari Berganti-ganti', 'Sukan Rakyat'].includes(match.sport_name)) ? (
                    <GroupEventRankings
                        match={match}
                        register={register}
                        watch={watch}
                        setValue={setValue}
                        sportRules={sportRules}
                    />
                ) : (
                    <>
                        {/* Set-based Score Input for multiple sports */}
                        {(['Bola Tampar', 'Bola Jaring', 'Bola Sepak', 'Ping Pong', 'Badminton'].includes(match.sport_name)) ? (
                            <SetBasedScoreInput
                                match={match}
                                register={register}
                                setValue={setValue}
                                selectedWinner={selectedWinner}
                            />
                        ) : (
                            /* Head-to-Head Score Input for other sports */
                            <HeadToHeadScoreInput
                                match={match}
                                register={register}
                                setValue={setValue}
                                selectedWinner={selectedWinner}
                                isPointBasedSport={false}
                            />
                        )}

                        {/* Walkover Section */}
                        <WalkoverSection
                            match={match}
                            setValue={setValue}
                            isWalkover={isWalkover}
                            setIsWalkover={setIsWalkover}
                            selectedWinner={selectedWinner}
                            setSelectedWinner={setSelectedWinner}
                        />
                    </>
                )}

                {/* Notes */}
                <div className="mb-6">
                    <Label htmlFor="notes">Catatan (Opsional)</Label>
                    <Textarea
                        id="notes"
                        {...register('notes')}
                        placeholder="Catatan tambahan tentang perlawanan..."
                        rows={2}
                        className="text-sm"
                    />
                </div>

                {/* Hidden Tournament Points Inputs */}
                <div className="hidden">
                    <Input {...register('points_zone_a', { valueAsNumber: true })} type="number" />
                    <Input {...register('points_zone_b', { valueAsNumber: true })} type="number" />
                    <Input {...register('winner_zone')} type="text" />
                </div>
            </div>

            {/* Fixed Footer */}
            <div className="sticky bottom-0 bg-white border-t border-gray-200 p-4 z-10">
                <div className="flex gap-3 justify-end">
                    <Button type="button" variant="outline" onClick={onClose} size="sm">
                        Batal
                    </Button>
                    <Button
                        type="submit"
                        disabled={isLoading}
                        size="sm"
                    >
                        {isLoading ? 'Menyimpan...' : 'Simpan Keputusan'}
                    </Button>
                </div>
            </div>
        </form>
    );
}
